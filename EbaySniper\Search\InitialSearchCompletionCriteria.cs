﻿using System;
using uBuyFirst.Prefs;

namespace uBuyFirst.Search
{
    /// <summary>
    /// Configuration for determining when initial search is complete
    /// Provides flexible, configurable criteria instead of magic numbers
    /// </summary>
    public class InitialSearchCompletionCriteria
    {
        /// <summary>
        /// Target number of items to process before considering completion
        /// Based on UserSettings.InitialResultsLimit with a small buffer
        /// </summary>
        public int TargetItemsProcessed { get; set; }

        /// <summary>
        /// Minimum time to run before allowing completion (prevents premature completion)
        /// </summary>
        public TimeSpan MinimumRunTime { get; set; } = TimeSpan.FromMinutes(3);

        /// <summary>
        /// Maximum time to wait before forcing completion (timeout protection)
        /// Calculated as 1 minute per 200 items based on InitialResultsLimit
        /// </summary>
        public TimeSpan MaximumRunTime { get; set; } = CalculateMaximumRunTime();

        /// <summary>
        /// Maximum allowed request queue size for stable completion
        /// </summary>
        public int MaxQueueSize { get; set; } = 5;

        /// <summary>
        /// Buffer to subtract from InitialResultsLimit for target calculation
        /// </summary>
        public int TargetBuffer { get; set; } = 3;

        /// <summary>
        /// Whether to require all search phases to be complete
        /// </summary>
        public bool RequireAllPhasesComplete { get; set; } = true;

        /// <summary>
        /// Whether to require request queue to be stable
        /// </summary>
        public bool RequireStableQueue { get; set; } = true;

        /// <summary>
        /// Time to wait after no new items are found before considering completion
        /// </summary>
        public TimeSpan NoNewItemsTimeout { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// Time to wait after all search requests are complete before considering completion
        /// </summary>
        public TimeSpan AllRequestsCompleteTimeout { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// Whether to use smart completion detection (recommended)
        /// </summary>
        public bool UseSmartCompletion { get; set; } = true;

        /// <summary>
        /// Calculates the maximum run time based on InitialResultsLimit
        /// Formula: 1 minute per 200 items, with a minimum of 4 minutes
        /// </summary>
        private static TimeSpan CalculateMaximumRunTime()
        {
            var minutes = Math.Max(UserSettings.InitialResultsLimit / 200.0, 4);
            return TimeSpan.FromMinutes(minutes);
        }

        /// <summary>
        /// Creates default completion criteria based on current settings
        /// </summary>
        public static InitialSearchCompletionCriteria CreateDefault()
        {
            return new InitialSearchCompletionCriteria
            {
                TargetItemsProcessed = Math.Max(UserSettings.InitialResultsLimit - 3, 1),
                MinimumRunTime = TimeSpan.FromMinutes(3),
                MaximumRunTime = CalculateMaximumRunTime(),
                MaxQueueSize = 5,
                TargetBuffer = 3,
                RequireAllPhasesComplete = true,
                RequireStableQueue = true,
                NoNewItemsTimeout = TimeSpan.FromMinutes(2),
                AllRequestsCompleteTimeout = TimeSpan.FromMinutes(1),
                UseSmartCompletion = true
            };
        }

        /// <summary>
        /// Creates criteria optimized for fast completion (testing/debugging)
        /// </summary>
        public static InitialSearchCompletionCriteria CreateFast()
        {
            return new InitialSearchCompletionCriteria
            {
                TargetItemsProcessed = Math.Max(UserSettings.InitialResultsLimit - 5, 1),
                MinimumRunTime = TimeSpan.FromSeconds(30), // Reduced but still reasonable
                MaximumRunTime = TimeSpan.FromMinutes(Math.Max(CalculateMaximumRunTime().TotalMinutes / 2, 2)), // Half of calculated time, min 2 minutes
                MaxQueueSize = 10,
                TargetBuffer = 5,
                RequireAllPhasesComplete = true,
                RequireStableQueue = false,
                NoNewItemsTimeout = TimeSpan.FromMinutes(1), // Faster for testing
                AllRequestsCompleteTimeout = TimeSpan.FromSeconds(30), // Faster for testing
                UseSmartCompletion = true
            };
        }

        /// <summary>
        /// Creates criteria optimized for thorough completion (production)
        /// </summary>
        public static InitialSearchCompletionCriteria CreateThorough()
        {
            return new InitialSearchCompletionCriteria
            {
                TargetItemsProcessed = Math.Max(UserSettings.InitialResultsLimit - 1, 1),
                MinimumRunTime = TimeSpan.FromMinutes(3),
                MaximumRunTime = TimeSpan.FromMinutes(Math.Max(CalculateMaximumRunTime().TotalMinutes * 1.5, 6)), // 1.5x calculated time, min 6 minutes
                MaxQueueSize = 3,
                TargetBuffer = 1,
                RequireAllPhasesComplete = true,
                RequireStableQueue = true,
                NoNewItemsTimeout = TimeSpan.FromMinutes(3), // More thorough
                AllRequestsCompleteTimeout = TimeSpan.FromMinutes(2), // More thorough
                UseSmartCompletion = true
            };
        }

        /// <summary>
        /// Updates the target and timing based on current InitialResultsLimit setting
        /// </summary>
        public void UpdateTargetFromSettings()
        {
            TargetItemsProcessed = Math.Max(UserSettings.InitialResultsLimit - TargetBuffer, 1);
            MaximumRunTime = CalculateMaximumRunTime();
        }

        /// <summary>
        /// Gets a description of the current criteria for debugging
        /// </summary>
        public string GetCriteriaDescription()
        {
            return $"Target: {TargetItemsProcessed} items | " +
                   $"Time: {MinimumRunTime:mm\\:ss} - {MaximumRunTime:mm\\:ss} (1min/200items) | " +
                   $"Smart: {(UseSmartCompletion ? $"NoItems={NoNewItemsTimeout:mm\\:ss}, AllDone={AllRequestsCompleteTimeout:mm\\:ss}" : "Off")} | " +
                   $"Queue: ≤{MaxQueueSize} | " +
                   $"Phases: {(RequireAllPhasesComplete ? "Required" : "Optional")} | " +
                   $"Stable: {(RequireStableQueue ? "Required" : "Optional")}";
        }
    }
}
