using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class CaptchaFallbackTests
    {
        [TestMethod]
        public void AutomatedCaptchaResult_Success_ShouldCreateSuccessResult()
        {
            // Act
            var result = AutomatedCaptchaResult.Success();
            
            // Assert
            Assert.IsTrue(result.IsSuccess);
            Assert.IsNull(result.ErrorMessage);
        }

        [TestMethod]
        public void AutomatedCaptchaResult_Failure_ShouldCreateFailureResult()
        {
            // Arrange
            var errorMessage = "Test error message";
            
            // Act
            var result = AutomatedCaptchaResult.Failure(errorMessage);
            
            // Assert
            Assert.IsFalse(result.IsSuccess);
            Assert.AreEqual(errorMessage, result.ErrorMessage);
        }

        [TestMethod]
        public void CaptchaCooldownManager_IsCaptchaError_WithCaptchaMessage_ShouldReturnTrue()
        {
            // Arrange
            var captchaMessage = "Session ID not found in session page HTML.";
            
            // Act
            var result = CaptchaCooldownManager.IsCaptchaError(captchaMessage);
            
            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void CaptchaCooldownManager_IsCaptchaError_WithNormalMessage_ShouldReturnFalse()
        {
            // Arrange
            var normalMessage = "Item not available";
            
            // Act
            var result = CaptchaCooldownManager.IsCaptchaError(normalMessage);
            
            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void CaptchaCooldownManager_IsInCooldown_InitialState_ShouldReturnFalse()
        {
            // Act
            var result = CaptchaCooldownManager.IsInCooldown;
            
            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void CaptchaCooldownManager_StartCooldown_ShouldActivateCooldown()
        {
            // Act
            CaptchaCooldownManager.StartCooldown();
            var result = CaptchaCooldownManager.IsInCooldown;
            
            // Assert
            Assert.IsTrue(result);
            
            // Cleanup
            CaptchaCooldownManager.ClearCooldown();
        }

        [TestMethod]
        public void CaptchaCooldownManager_ClearCooldown_ShouldDeactivateCooldown()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();
            
            // Act
            CaptchaCooldownManager.ClearCooldown();
            var result = CaptchaCooldownManager.IsInCooldown;
            
            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void CaptchaCooldownManager_GetStatusMessage_WithActiveCooldown_ShouldReturnCooldownMessage()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();
            
            // Act
            var message = CaptchaCooldownManager.GetStatusMessage();
            
            // Assert
            Assert.IsTrue(message.Contains("captcha"));
            Assert.IsTrue(message.Contains("paused"));
            
            // Cleanup
            CaptchaCooldownManager.ClearCooldown();
        }

        [TestMethod]
        public void CaptchaCooldownManager_GetStatusMessage_WithoutCooldown_ShouldReturnActiveMessage()
        {
            // Arrange
            CaptchaCooldownManager.ClearCooldown();
            
            // Act
            var message = CaptchaCooldownManager.GetStatusMessage();
            
            // Assert
            Assert.AreEqual("Restock auto purchasing is active", message);
        }

        [TestMethod]
        public void CaptchaCooldownManager_RemainingCooldownTime_WithoutCooldown_ShouldReturnNull()
        {
            // Arrange
            CaptchaCooldownManager.ClearCooldown();
            
            // Act
            var remaining = CaptchaCooldownManager.RemainingCooldownTime;
            
            // Assert
            Assert.IsNull(remaining);
        }

        [TestMethod]
        public void CaptchaCooldownManager_RemainingCooldownTime_WithActiveCooldown_ShouldReturnTimeSpan()
        {
            // Arrange
            CaptchaCooldownManager.StartCooldown();
            
            // Act
            var remaining = CaptchaCooldownManager.RemainingCooldownTime;
            
            // Assert
            Assert.IsNotNull(remaining);
            Assert.IsTrue(remaining.Value.TotalSeconds > 0);
            
            // Cleanup
            CaptchaCooldownManager.ClearCooldown();
        }
    }
}
