namespace uBuyFirst.Search
{
    /// <summary>
    /// Interface for tracking initial search progress
    /// Allows for easy testing and different implementations
    /// </summary>
    public interface IInitialSearchProgressTracker
    {
        /// <summary>
        /// Gets the current progress state
        /// </summary>
        InitialSearchProgress Progress { get; }

        /// <summary>
        /// Initializes progress tracking for a new initial search
        /// </summary>
        /// <param name="isDualPhaseEnabled">Whether dual-phase search is enabled</param>
        void Initialize(bool isDualPhaseEnabled);

        /// <summary>
        /// Records that items were found during search
        /// </summary>
        /// <param name="count">Number of items found</param>
        void RecordItemsFound(int count = 1);

        /// <summary>
        /// Records that items were fully processed
        /// </summary>
        /// <param name="count">Number of items processed</param>
        void RecordItemsProcessed(int count = 1);

        /// <summary>
        /// Records that API requests were completed
        /// </summary>
        /// <param name="count">Number of requests completed</param>
        void RecordRequestsCompleted(int count = 1);

        /// <summary>
        /// Records that keywords completed their initial search
        /// </summary>
        /// <param name="count">Number of keywords processed</param>
        void RecordKeywordsProcessed(int count = 1);

        /// <summary>
        /// Marks the price-first phase as complete
        /// </summary>
        void MarkPriceFirstPhaseComplete();

        /// <summary>
        /// Marks the regular search phase as complete
        /// </summary>
        void MarkRegularPhaseComplete();

        /// <summary>
        /// Marks that all search requests have been completed
        /// </summary>
        void MarkAllRequestsComplete();

        /// <summary>
        /// Gets whether all search phases are complete
        /// </summary>
        bool AreAllPhasesComplete { get; }

        /// <summary>
        /// Gets a summary of current progress for debugging
        /// </summary>
        string GetProgressSummary();
    }

    /// <summary>
    /// Default implementation of IInitialSearchProgressTracker
    /// </summary>
    public class InitialSearchProgressTracker : IInitialSearchProgressTracker
    {
        private readonly InitialSearchProgress _progress;

        public InitialSearchProgress Progress => _progress;

        public InitialSearchProgressTracker()
        {
            _progress = new InitialSearchProgress();
        }

        public void Initialize(bool isDualPhaseEnabled)
        {
            _progress.Initialize(isDualPhaseEnabled);
        }

        public void RecordItemsFound(int count = 1)
        {
            _progress.IncrementItemsFound(count);
        }

        public void RecordItemsProcessed(int count = 1)
        {
            _progress.IncrementItemsProcessed(count);
        }

        public void RecordRequestsCompleted(int count = 1)
        {
            _progress.IncrementRequestsCompleted(count);
        }

        public void RecordKeywordsProcessed(int count = 1)
        {
            _progress.IncrementKeywordsProcessed(count);
        }

        public void MarkPriceFirstPhaseComplete()
        {
            _progress.MarkPriceFirstPhaseComplete();
        }

        public void MarkRegularPhaseComplete()
        {
            _progress.MarkRegularPhaseComplete();
        }

        public void MarkAllRequestsComplete()
        {
            _progress.MarkAllRequestsComplete();
        }

        public bool AreAllPhasesComplete => _progress.AreAllPhasesComplete;

        public string GetProgressSummary()
        {
            return _progress.GetProgressSummary();
        }
    }
}
