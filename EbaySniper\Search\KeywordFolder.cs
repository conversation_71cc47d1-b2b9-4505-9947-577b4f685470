﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using DevExpress.XtraTreeList;
using System.Reflection;
using System.Windows.Forms;
using uBuyFirst.BulkEdit;

namespace uBuyFirst.Search
{
    [Serializable]
    [Obfuscation(Exclude = true)]
    [XmlInclude(typeof(Keyword2Find))]
    public class KeywordFolder : TreeList.IVirtualTreeListData
    {
        /// <summary>
        /// Event raised when bulk changes are applied and TreeList needs to be refreshed
        /// </summary>
        public static event EventHandler<BulkChangesAppliedEventArgs> BulkChangesApplied;

        /// <summary>
        /// Event arguments for bulk changes applied event
        /// </summary>
        public class BulkChangesAppliedEventArgs : EventArgs
        {
            public KeywordFolder ChangedFolder { get; set; }
            public string PropertyName { get; set; }
        }
        #region Core Properties

        /// <summary>
        /// Unique identifier for the folder
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Display name of the folder
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// Whether the folder is expanded in the TreeList
        /// </summary>
        public bool IsExpanded { get; set; } = true;

        /// <summary>
        /// Optional description for the folder
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        #endregion

        #region Hierarchy Relationships

        /// <summary>
        /// Parent folder (null for root-level folders)
        /// </summary>
        [XmlIgnore]
        public KeywordFolder ParentFolder { get; set; }

        /// <summary>
        /// Child folders (nested folders)
        /// </summary>
        public List<KeywordFolder> Children { get; set; } = new List<KeywordFolder>();

        /// <summary>
        /// Keywords contained in this folder
        /// </summary>
        public List<Keyword2Find> Keywords { get; set; } = new List<Keyword2Find>();

        #endregion

        #region TreeList Integration

        /// <summary>
        /// Provides child nodes for TreeList virtual mode
        /// </summary>
        public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
        {
            var children = new List<object>();
            children.AddRange(Children);      // Add child folders first
            children.AddRange(Keywords);      // Then add keywords
            info.Children = children;
        }

        /// <summary>
        /// Provides cell values for TreeList columns
        /// </summary>
        public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
        {
            switch (info.Column.Caption)
            {
                case "Enabled":
                    // Convert CheckState to boolean for ToggleSwitch control
                    var folderState = GetFolderEnabledState();
                    var boolValue = folderState == CheckState.Checked;
                    info.CellData = boolValue;
                    break;
                case "Alias":
                    info.CellData = Name;
                    break;
                case "Keywords":
                    var totalKeywordCount = GetAllKeywords().Count;
                    info.CellData = $"[{totalKeywordCount} keywords]";
                    break;

                // Bulk-editable columns - show representative values
                case "Price Min":
                    info.CellData = GetRepresentativePriceMin();
                    break;
                case "Price Max":
                    info.CellData = GetRepresentativePriceMax();
                    break;
                case "Threads":
                    info.CellData = GetRepresentativeThreads();
                    break;
                case "Search in description":
                    info.CellData = GetRepresentativeSearchInDescription();
                    break;
                case "Located in":
                    info.CellData = GetRepresentativeLocatedIn();
                    break;
                case "Ships to":
                    info.CellData = GetRepresentativeShipsTo();
                    break;
                case "Seller type":
                    info.CellData = GetRepresentativeSellerType();
                    break;
                case "Site":
                    info.CellData = GetRepresentativeSite();
                    break;
                case "View":
                    info.CellData = GetRepresentativeViewName();
                    break;
                case "Condition":
                    info.CellData = GetRepresentativeCondition();
                    break;
                case "Category ID":
                    info.CellData = GetRepresentativeCategoryId();
                    break;
                case "Type":
                case "ListingType":
                    info.CellData = GetRepresentativeListingType();
                    break;
                case "Sellers":
                    info.CellData = GetRepresentativeSellers();
                    break;
                case "Ship Zipcode":
                    info.CellData = GetRepresentativeZip();
                    break;
                case "Interval":
                    info.CellData = GetRepresentativeInterval();
                    break;

                default:
                    info.CellData = ""; // Empty for other columns
                    break;
            }
        }

        /// <summary>
        /// Handles cell value changes (e.g., folder rename, checkbox clicks, bulk property changes)
        /// </summary>
        public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
        {
            switch (info.Column.Caption)
            {
                case "Alias":
                    if (info.NewCellData is string newName)
                    {
                        Name = newName;
                    }
                    break;
                case "Enabled":
                    // Handle checkbox clicks - this will be processed by treeList1_AfterCheckNode
                    // The actual tristate logic is handled there, not here
                    break;

                // Bulk editing operations
                case "Price Min":
                    ApplyBulkPriceMinChange(info.NewCellData);
                    break;
                case "Price Max":
                    ApplyBulkPriceMaxChange(info.NewCellData);
                    break;
                case "Threads":
                    ApplyBulkThreadsChange(info.NewCellData);
                    break;
                case "Search in description":
                    ApplyBulkSearchInDescriptionChange(info.NewCellData);
                    break;
                case "Located in":
                    ApplyBulkLocatedInChange(info.NewCellData);
                    break;
                case "Ships to":
                    ApplyBulkShipsToChange(info.NewCellData);
                    break;
                case "Seller type":
                    ApplyBulkSellerTypeChange(info.NewCellData);
                    break;
                case "Site":
                    ApplyBulkSiteChange(info.NewCellData);
                    break;
                case "View":
                    ApplyBulkViewNameChange(info.NewCellData);
                    break;
                case "Condition":
                    ApplyBulkConditionChange(info.NewCellData);
                    break;
                case "Category ID":
                    ApplyBulkCategoryIdChange(info.NewCellData);
                    break;
                case "Type":
                case "ListingType":
                    ApplyBulkListingTypeChange(info.NewCellData);
                    break;
                case "Sellers":
                    ApplyBulkSellersChange(info.NewCellData);
                    break;
                case "Ship Zipcode":
                    ApplyBulkZipChange(info.NewCellData);
                    break;
                case "Interval":
                    ApplyBulkIntervalChange(info.NewCellData);
                    break;
            }
        }

        #endregion

        #region Bulk Change Operations

        /// <summary>
        /// Applies bulk PriceMin change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkPriceMinChange(object newValue)
        {
            if (double.TryParse(newValue?.ToString(), out var priceMin) && priceMin >= 0)
            {
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["PriceMin"] = priceMin };
                ApplyBulkChangesToKeywords(keywords, changes, "Price Min");
            }
        }

        /// <summary>
        /// Applies bulk PriceMax change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkPriceMaxChange(object newValue)
        {
            if (double.TryParse(newValue?.ToString(), out var priceMax) && priceMax >= 0)
            {
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["PriceMax"] = priceMax };
                ApplyBulkChangesToKeywords(keywords, changes, "Price Max");
            }
        }

        /// <summary>
        /// Applies bulk Threads change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkThreadsChange(object newValue)
        {
            if (int.TryParse(newValue?.ToString(), out var threads) && threads >= 1 && threads <= 10)
            {
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["Threads"] = threads };
                ApplyBulkChangesToKeywords(keywords, changes, "Threads");
            }
        }

        /// <summary>
        /// Applies bulk SearchInDescription change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkSearchInDescriptionChange(object newValue)
        {
            if (newValue is bool searchInDesc)
            {
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["SearchInDescription"] = searchInDesc };
                ApplyBulkChangesToKeywords(keywords, changes, "Search in Description");
            }
        }

        /// <summary>
        /// Applies bulk LocatedIn change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkLocatedInChange(object newValue)
        {
            var locatedIn = newValue?.ToString() ?? "";
            var keywords = GetAllKeywords();
            var changes = new Dictionary<string, object> { ["LocatedIn"] = locatedIn };
            ApplyBulkChangesToKeywords(keywords, changes, "Located In");
        }

        /// <summary>
        /// Applies bulk AvailableTo change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkShipsToChange(object newValue)
        {
            var availableTo = newValue?.ToString() ?? "";
            var keywords = GetAllKeywords();
            var changes = new Dictionary<string, object> { ["AvailableTo"] = availableTo };
            ApplyBulkChangesToKeywords(keywords, changes, "Ships To");
        }

        /// <summary>
        /// Applies bulk SellerType change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkSellerTypeChange(object newValue)
        {
            string sellerType = "";

            // Handle different types of input from the SellerType combo box
            if (newValue is string stringValue)
            {
                sellerType = stringValue;
            }
            else if (newValue != null)
            {
                sellerType = newValue.ToString();
            }

            var keywords = GetAllKeywords();
            var changes = new Dictionary<string, object> { ["SellerType"] = sellerType };
            ApplyBulkChangesToKeywords(keywords, changes, "Seller Type");
        }

        /// <summary>
        /// Applies bulk EbaySiteName change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkSiteChange(object newValue)
        {
            string siteName = "";

            // Handle different types of input from the Site dropdown
            if (newValue is string stringValue)
            {
                siteName = stringValue;
            }
            else if (newValue != null)
            {
                siteName = newValue.ToString();
            }

            if (!string.IsNullOrWhiteSpace(siteName))
            {
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["EbaySiteName"] = siteName };
                ApplyBulkChangesToKeywords(keywords, changes, "Site");
            }
        }

        /// <summary>
        /// Applies bulk ViewName change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkViewNameChange(object newValue)
        {
            string viewName = "";

            // Handle different types of input from the MRU editor
            if (newValue is string stringValue)
            {
                viewName = stringValue;
            }
            else if (newValue != null)
            {
                viewName = newValue.ToString();
            }

            if (!string.IsNullOrWhiteSpace(viewName))
            {
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["ViewName"] = viewName };
                ApplyBulkChangesToKeywords(keywords, changes, "View Name");
            }
        }

        /// <summary>
        /// Applies bulk Condition change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkConditionChange(object newValue)
        {
            string[] conditionArray = null;

            // Handle different types of input from the Condition checked combo box
            if (newValue is string stringValue && !string.IsNullOrWhiteSpace(stringValue))
            {
                // Parse comma-separated values from checked combo box
                conditionArray = stringValue.Split(',').Select(s => s.Trim()).Where(s => !string.IsNullOrEmpty(s)).ToArray();
            }
            else if (newValue is string[] arrayValue)
            {
                conditionArray = arrayValue;
            }

            if (conditionArray != null && conditionArray.Length > 0)
            {
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["Condition"] = conditionArray };
                ApplyBulkChangesToKeywords(keywords, changes, "Condition");
            }
        }

        /// <summary>
        /// Applies bulk CategoryId change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkCategoryIdChange(object newValue)
        {
            string categoryId = "";

            // Handle different types of input from the TreeList lookup
            if (newValue is string stringValue)
            {
                categoryId = stringValue;
            }
            else if (newValue is GUI.Category category)
            {
                categoryId = category.CategoryID;
            }
            else if (newValue != null)
            {
                categoryId = newValue.ToString();
            }

            // Debug output to understand what value is being applied (can be removed in production)
            System.Diagnostics.Debug.WriteLine($"ApplyBulkCategoryIdChange: Folder '{Name}' setting category to '{categoryId}'");

            var keywords = GetAllKeywords();
            var changes = new Dictionary<string, object> { ["Categories4Api"] = categoryId };
            ApplyBulkChangesToKeywords(keywords, changes, "Category ID");
        }

        /// <summary>
        /// Applies bulk ListingType change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkListingTypeChange(object newValue)
        {
            ListingType[] listingTypeArray = null;

            // Handle different types of input from the ListingType checked combo box
            if (newValue is string stringValue && !string.IsNullOrWhiteSpace(stringValue))
            {
                try
                {
                    // Parse comma-separated values from checked combo box (same logic as individual keyword)
                    var typeNames = stringValue.Split(',').Select(s => s.Trim()).Where(s => !string.IsNullOrEmpty(s));
                    var types = new List<ListingType>();

                    foreach (var typeName in typeNames)
                    {
                        if (Enum.TryParse<ListingType>(typeName, out var listingType))
                        {
                            types.Add(listingType);
                        }
                    }

                    if (types.Count > 0)
                    {
                        // Handle OutOfStock special case (same logic as individual keyword)
                        if (types.Contains(ListingType.OutOfStock) && types.Count > 1)
                        {
                            listingTypeArray = new[] { ListingType.OutOfStock };
                            System.Windows.Forms.MessageBox.Show(
                                "Out of stock monitoring is enabled.\r\n" +
                                "Please, enter your items IDs separated by comma into \"Keywords\" column.\r\n" +
                                "BuyItNow and Auction search have been disabled.",
                                "Out of Stock Mode",
                                System.Windows.Forms.MessageBoxButtons.OK,
                                System.Windows.Forms.MessageBoxIcon.Information);
                        }
                        else
                        {
                            listingTypeArray = types.Distinct().ToArray();
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log error and return without applying changes
                    return;
                }
            }
            else if (newValue is ListingType[] arrayValue)
            {
                listingTypeArray = arrayValue;
            }

            if (listingTypeArray != null && listingTypeArray.Length > 0)
            {
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["ListingType"] = listingTypeArray };
                ApplyBulkChangesToKeywords(keywords, changes, "Listing Type");
            }
        }

        /// <summary>
        /// Applies bulk Sellers change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkSellersChange(object newValue)
        {
            var sellersValue = newValue?.ToString() ?? "";
            if (!string.IsNullOrWhiteSpace(sellersValue))
            {
                // Convert single seller string to array format
                var sellersArray = new string[] { sellersValue };
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["Sellers"] = sellersArray };
                ApplyBulkChangesToKeywords(keywords, changes, "Sellers");
            }
        }

        /// <summary>
        /// Applies bulk Zip change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkZipChange(object newValue)
        {
            var zip = newValue?.ToString() ?? "";
            var keywords = GetAllKeywords();
            var changes = new Dictionary<string, object> { ["Zip"] = zip };
            ApplyBulkChangesToKeywords(keywords, changes, "Ship Zipcode");
        }

        /// <summary>
        /// Applies bulk Frequency (Interval) change to all keywords in this folder and subfolders
        /// </summary>
        private void ApplyBulkIntervalChange(object newValue)
        {
            TimeSpan? timeSpan = null;

            // Handle different types of input from the TimeSpan editor
            if (newValue is TimeSpan directTimeSpan)
            {
                timeSpan = directTimeSpan;
            }
            else if (newValue != null && TimeSpan.TryParse(newValue.ToString(), out var parsedTimeSpan))
            {
                timeSpan = parsedTimeSpan;
            }

            if (timeSpan.HasValue)
            {
                var keywords = GetAllKeywords();
                var changes = new Dictionary<string, object> { ["Frequency"] = timeSpan.Value };
                ApplyBulkChangesToKeywords(keywords, changes, "Interval");
            }
        }

        /// <summary>
        /// Common method to apply bulk changes to keywords with validation and feedback
        /// </summary>
        private void ApplyBulkChangesToKeywords(List<Keyword2Find> keywords, Dictionary<string, object> changes, string propertyDisplayName)
        {
            if (!keywords.Any()) return;

            try
            {
                // Use existing BulkPropertyEditor for validation and application
                var bulkEditor = new BulkEdit.BulkPropertyEditor();

                // Validate the changes
                var validationErrors = bulkEditor.ValidatePropertyValues(changes);
                if (validationErrors.Any())
                {
                    // Show validation error (could be enhanced with better UI feedback)
                    System.Windows.Forms.MessageBox.Show(
                        $"Invalid value for {propertyDisplayName}:\n{string.Join("\n", validationErrors)}",
                        "Validation Error",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Warning);
                    return;
                }

                // Apply the changes
                var result = bulkEditor.ApplyBulkChanges(keywords, changes);

                // Trigger TreeList refresh to show updated values
                RefreshTreeListAfterBulkChange(propertyDisplayName);

                // Provide feedback - only show errors, success is indicated by the visual refresh
                if (!result.Success)
                {
                    System.Windows.Forms.MessageBox.Show(
                        $"Failed to apply {propertyDisplayName} changes:\n{result.GetErrorsText()}",
                        "Bulk Edit Error",
                        System.Windows.Forms.MessageBoxButtons.OK,
                        System.Windows.Forms.MessageBoxIcon.Error);
                }
                // Success is indicated by the TreeList refresh showing updated values
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show(
                    $"Error applying bulk changes to {propertyDisplayName}: {ex.Message}",
                    "Error",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Refreshes the TreeList to show updated values after bulk changes
        /// </summary>
        private void RefreshTreeListAfterBulkChange(string propertyName = "")
        {
            // Raise event to notify Form1 to refresh the TreeList
            var eventArgs = new BulkChangesAppliedEventArgs
            {
                ChangedFolder = this,
                PropertyName = propertyName
            };
            BulkChangesApplied?.Invoke(this, eventArgs);
        }

        #endregion

        #region Bulk Editing Helper Methods

        /// <summary>
        /// Gets representative PriceMin value for display in folder cells
        /// </summary>
        private object GetRepresentativePriceMin()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.PriceMin).Distinct().ToList();
            if (values.Count == 1) return values[0]; // All same value

            var min = values.Min();
            var max = values.Max();
            return $"{min:F0}-{max:F0}"; // Show range
        }

        /// <summary>
        /// Gets representative PriceMax value for display in folder cells
        /// </summary>
        private object GetRepresentativePriceMax()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.PriceMax).Distinct().ToList();
            if (values.Count == 1) return values[0]; // All same value

            var min = values.Min();
            var max = values.Max();
            return $"{min:F0}-{max:F0}"; // Show range
        }

        /// <summary>
        /// Gets representative Threads value for display in folder cells
        /// </summary>
        private object GetRepresentativeThreads()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.Threads).Distinct().ToList();
            if (values.Count == 1) return values[0]; // All same value

            var min = values.Min();
            var max = values.Max();
            return $"{min}-{max}"; // Show range
        }

        /// <summary>
        /// Gets representative SearchInDescription value for display in folder cells
        /// </summary>
        private object GetRepresentativeSearchInDescription()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.SearchInDescription).Distinct().ToList();
            if (values.Count == 1) return values[0]; // All same value

            return "Mixed"; // Mixed values
        }

        /// <summary>
        /// Gets representative LocatedIn value for display in folder cells
        /// </summary>
        private object GetRepresentativeLocatedIn()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.LocatedIn).Where(v => !string.IsNullOrEmpty(v)).Distinct().ToList();
            if (values.Count == 0) return "";
            if (values.Count == 1) return values[0]; // All same value

            return "Mixed"; // Mixed values
        }

        /// <summary>
        /// Gets representative AvailableTo value for display in folder cells
        /// </summary>
        private object GetRepresentativeShipsTo()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.AvailableTo).Where(v => !string.IsNullOrEmpty(v)).Distinct().ToList();
            if (values.Count == 0) return "";
            if (values.Count == 1) return values[0]; // All same value

            return "Mixed"; // Mixed values
        }

        /// <summary>
        /// Gets representative SellerType value for display in folder cells
        /// </summary>
        private object GetRepresentativeSellerType()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.SellerType).Where(v => !string.IsNullOrEmpty(v)).Distinct().ToList();
            if (values.Count == 0) return "";
            if (values.Count == 1) return values[0]; // All same value

            return "Mixed"; // Mixed values
        }

        /// <summary>
        /// Gets representative EbaySiteName value for display in folder cells
        /// </summary>
        private object GetRepresentativeSite()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.EbaySiteName).Where(v => !string.IsNullOrEmpty(v)).Distinct().ToList();
            if (values.Count == 0) return "";
            if (values.Count == 1) return values[0]; // All same value

            return "Mixed"; // Mixed values
        }

        /// <summary>
        /// Gets representative ViewName value for display in folder cells
        /// </summary>
        private object GetRepresentativeViewName()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.ViewName).Where(v => !string.IsNullOrEmpty(v)).Distinct().ToList();
            if (values.Count == 0) return "";
            if (values.Count == 1) return values[0]; // All same value

            return "Mixed"; // Mixed values
        }

        /// <summary>
        /// Gets representative Condition value for display in folder cells
        /// </summary>
        private object GetRepresentativeCondition()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            // Condition is a string array, so we need to handle it differently
            var allConditions = keywords.Where(k => k.Condition != null && k.Condition.Length > 0)
                                      .SelectMany(k => k.Condition)
                                      .Distinct()
                                      .ToList();

            if (allConditions.Count == 0) return "";
            if (allConditions.Count == 1) return allConditions[0]; // All same condition

            return $"Mixed ({allConditions.Count} conditions)"; // Multiple conditions
        }

        /// <summary>
        /// Gets representative CategoryId value for display in folder cells
        /// </summary>
        private object GetRepresentativeCategoryId()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            // Get all category values (including empty ones for analysis)
            var allValues = keywords.Select(k => k.Categories4Api ?? "").ToList();
            var nonEmptyValues = allValues.Where(v => !string.IsNullOrEmpty(v)).Distinct().ToList();
            var emptyCount = allValues.Count(v => string.IsNullOrEmpty(v));

            // Debug output to understand what's happening (can be removed in production)
            System.Diagnostics.Debug.WriteLine($"GetRepresentativeCategoryId: Folder '{Name}' has {keywords.Count} keywords");
            System.Diagnostics.Debug.WriteLine($"  Non-empty distinct values: [{string.Join(", ", nonEmptyValues.Select(v => $"'{v}'"))}]");
            System.Diagnostics.Debug.WriteLine($"  Empty values: {emptyCount}");

            // If no non-empty values, return empty
            if (nonEmptyValues.Count == 0) return "";

            // If there's only one distinct non-empty value, show it (even if some keywords are empty)
            // This addresses the user's issue where they want to see the actual category ID
            if (nonEmptyValues.Count == 1)
            {
                var categoryId = nonEmptyValues[0];
                // If some keywords are empty, show a hint about that
                if (emptyCount > 0)
                {
                    return $"{categoryId} ({emptyCount} empty)";
                }
                return categoryId;
            }

            // Multiple different non-empty values - genuinely mixed
            return $"Mixed ({nonEmptyValues.Count} different)";
        }

        /// <summary>
        /// Gets representative ListingType value for display in folder cells
        /// </summary>
        private object GetRepresentativeListingType()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            // ListingType is an array, so we need to handle it differently
            var allTypes = keywords.Where(k => k.ListingType != null && k.ListingType.Length > 0)
                                  .SelectMany(k => k.ListingType)
                                  .Distinct()
                                  .ToList();

            if (allTypes.Count == 0) return "";
            if (allTypes.Count == 1) return allTypes[0].ToString(); // All same type

            return $"Mixed ({allTypes.Count} types)"; // Multiple types
        }

        /// <summary>
        /// Gets representative Sellers value for display in folder cells
        /// </summary>
        private object GetRepresentativeSellers()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            // Sellers is a string array, so we need to handle it differently
            var allSellers = keywords.Where(k => k.Sellers != null && k.Sellers.Length > 0)
                                    .SelectMany(k => k.Sellers)
                                    .Distinct()
                                    .ToList();

            if (allSellers.Count == 0) return "";
            if (allSellers.Count == 1) return allSellers[0]; // All same seller

            return $"Mixed ({allSellers.Count} sellers)"; // Multiple sellers
        }

        /// <summary>
        /// Gets representative Zip value for display in folder cells
        /// </summary>
        private object GetRepresentativeZip()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.Zip).Where(v => !string.IsNullOrEmpty(v)).Distinct().ToList();
            if (values.Count == 0) return "";
            if (values.Count == 1) return values[0]; // All same value

            return "Mixed"; // Mixed values
        }

        /// <summary>
        /// Gets representative Frequency (Interval) value for display in folder cells
        /// </summary>
        private object GetRepresentativeInterval()
        {
            var keywords = GetAllKeywords();
            if (!keywords.Any()) return "";

            var values = keywords.Select(k => k.Frequency).Distinct().ToList();
            if (values.Count == 1) return values[0]; // All same value

            var min = values.Min();
            var max = values.Max();
            return $"{min} - {max}"; // Show range
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Gets the full path of the folder (e.g., "Electronics > Mobile Phones")
        /// </summary>
        public string GetFullPath()
        {
            if (ParentFolder == null) return Name;

            var pathParts = new List<string>();
            var current = this;

            while (current != null)
            {
                pathParts.Insert(0, current.Name);
                current = current.ParentFolder;
            }

            return string.Join(" > ", pathParts);
        }

        /// <summary>
        /// Gets all keywords in this folder and all subfolders
        /// </summary>
        public List<Keyword2Find> GetAllKeywords()
        {
            var allKeywords = new List<Keyword2Find>(Keywords);

            foreach (var childFolder in Children)
            {
                allKeywords.AddRange(childFolder.GetAllKeywords());
            }

            return allKeywords;
        }

        /// <summary>
        /// Checks if any descendant (keywords or child terms) in this folder or subfolders is enabled
        /// This is used for the new recursive folder state calculation logic
        /// </summary>
        public bool HasAnyEnabledDescendants()
        {
            // Check all keywords in this folder and subfolders
            var allKeywords = GetAllKeywords();

            foreach (var keyword in allKeywords)
            {
                // Check if the keyword itself is enabled
                if (keyword.KeywordEnabled == CheckState.Checked || keyword.KeywordEnabled == CheckState.Indeterminate)
                {
                    return true;
                }

                // Check if any child terms of this keyword are enabled
                foreach (var childTerm in keyword.ChildrenCore)
                {
                    if (childTerm.Enabled)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Calculates the check state for a folder based on ALL descendants (keywords and child terms) recursively
        /// This implements the user's requested behavior: parent folder disabled only when ALL children recursively are deselected
        /// Folder is enabled if ANY descendant is enabled, disabled only when ALL descendants are disabled
        /// </summary>
        public CheckState CalculateRecursiveFolderCheckState()
        {
            // If folder has no children at all, it's unchecked
            if (!Keywords.Any() && !Children.Any())
            {
                return CheckState.Unchecked;
            }

            // Use the simpler HasAnyEnabledDescendants check
            // If ANY descendant is enabled, folder should be checked
            // Only when ALL descendants are disabled should folder be unchecked
            return HasAnyEnabledDescendants() ? CheckState.Checked : CheckState.Unchecked;
        }

        /// <summary>
        /// Gets all folders in this hierarchy (including this folder)
        /// </summary>
        public List<KeywordFolder> GetAllFolders()
        {
            var allFolders = new List<KeywordFolder> { this };

            foreach (var childFolder in Children)
            {
                allFolders.AddRange(childFolder.GetAllFolders());
            }

            return allFolders;
        }

        /// <summary>
        /// Checks if this folder can accept a dropped item
        /// </summary>
        public bool CanAcceptDrop(object draggedItem)
        {
            switch (draggedItem)
            {
                case Keyword2Find keyword:
                    return true; // Folders can always accept keywords
                case KeywordFolder folder:
                    return !folder.IsAncestorOf(this); // Prevent circular references
                default:
                    return false;
            }
        }

        /// <summary>
        /// Handles dropping an item into this folder
        /// </summary>
        public void AcceptDrop(object draggedItem, int position = -1)
        {
            switch (draggedItem)
            {
                case Keyword2Find keyword:
                    // Remove from current parent
                    keyword.ParentFolder?.Keywords.Remove(keyword);

                    // Add to this folder
                    if (position >= 0 && position < Keywords.Count)
                        Keywords.Insert(position, keyword);
                    else
                        Keywords.Add(keyword);

                    keyword.ParentFolder = this;
                    break;

                case KeywordFolder folder:
                    if (CanAcceptDrop(folder))
                    {
                        // Remove from current parent
                        folder.ParentFolder?.Children.Remove(folder);

                        // Add to this folder
                        if (position >= 0 && position < Children.Count)
                            Children.Insert(position, folder);
                        else
                            Children.Add(folder);

                        folder.ParentFolder = this;
                    }
                    break;
            }
        }

        /// <summary>
        /// Checks if this folder is an ancestor of the specified folder
        /// </summary>
        internal bool IsAncestorOf(KeywordFolder folder)
        {
            var current = folder.ParentFolder;
            while (current != null)
            {
                if (current == this) return true;
                current = current.ParentFolder;
            }
            return false;
        }

        /// <summary>
        /// Validates the folder hierarchy for circular references
        /// </summary>
        public bool ValidateHierarchy()
        {
            var visited = new HashSet<string>();
            return ValidateHierarchyRecursive(visited);
        }

        private bool ValidateHierarchyRecursive(HashSet<string> visited)
        {
            if (visited.Contains(Id)) return false; // Circular reference detected

            visited.Add(Id);

            foreach (var child in Children)
            {
                if (!child.ValidateHierarchyRecursive(new HashSet<string>(visited)))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Gets the root QueryList by traversing up the hierarchy
        /// </summary>
        public QueryList GetRootQueryList()
        {
            // This would need to be implemented based on how the QueryList maintains references
            // For now, return null as this is primarily for backward compatibility
            return null;
        }

        /// <summary>
        /// Gets all sibling folders (folders at the same level)
        /// </summary>
        public List<KeywordFolder> GetSiblingFolders()
        {
            if (ParentFolder != null)
            {
                return ParentFolder.Children.Where(f => f != this).ToList();
            }
            else
            {
                // This is a root folder, need to get siblings from QueryList
                // For now, return empty list - this will be handled by the QueryList methods
                return new List<KeywordFolder>();
            }
        }

        /// <summary>
        /// Checks if a folder name already exists among siblings
        /// </summary>
        public bool IsNameTakenBySibling(string name)
        {
            var siblings = GetSiblingFolders();
            return siblings.Any(f => f.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Generates a unique folder name by adding numbers if needed
        /// </summary>
        public static string GenerateUniqueName(string baseName, List<KeywordFolder> existingFolders)
        {
            if (!existingFolders.Any(f => f.Name.Equals(baseName, StringComparison.OrdinalIgnoreCase)))
            {
                return baseName; // Name is already unique
            }

            int counter = 2;
            string uniqueName;

            do
            {
                uniqueName = $"{baseName} ({counter})";
                counter++;
            }
            while (existingFolders.Any(f => f.Name.Equals(uniqueName, StringComparison.OrdinalIgnoreCase)));

            return uniqueName;
        }

        /// <summary>
        /// Gets the enabled state of the folder based on ALL descendants (keywords and child terms) recursively
        /// This implements the user's requested behavior: parent folder disabled only when ALL children recursively are deselected
        /// Only folders can have indeterminate state
        /// </summary>
        private CheckState GetFolderEnabledState()
        {
            // Use the new recursive logic
            return CalculateRecursiveFolderCheckState();
        }

        #endregion
    }
}
