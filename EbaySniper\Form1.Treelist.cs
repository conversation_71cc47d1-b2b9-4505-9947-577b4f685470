﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using DevExpress.Data.Filtering;
using DevExpress.Data.Filtering.Helpers;
using DevExpress.Skins;
using DevExpress.Utils;
using DevExpress.Utils.Menu;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Filtering;
using DevExpress.XtraEditors.ViewInfo;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Menu;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList.ViewInfo;
using eBay.Service.Core.Soap;
using uBuyFirst.Filters;
using uBuyFirst.Grid;
using uBuyFirst.Network;
using uBuyFirst.Prefs;
using uBuyFirst.Stats;
using uBuyFirst.SubSearch;
using uBuyFirst.Tools;
using PopupMenuShowingEventArgs = DevExpress.XtraTreeList.PopupMenuShowingEventArgs;

namespace uBuyFirst
{
    public partial class Form1
    {
        private TreeListHitInfo _pressedHitInfo;

        private void CheckSelected_ItemClick(object sender, EventArgs e)
        {
            // Create a copy of the selection to avoid collection modification exception
            var selectedNodes = treeList1.Selection.ToArray();

            // Process all nodes without refreshing UI for each one (batch operation)
            foreach (var node in selectedNodes)
            {
                // Use centralized logic to handle checking - force to Checked state
                HandleCheckboxStateChange(node, CheckState.Checked, refreshUI: false);
            }

            // Refresh UI once after all changes are complete
            treeList1.RefreshDataSource();
        }

        private void UnCheckSelected_ItemClick(object sender, EventArgs e)
        {
            // Create a copy of the selection to avoid collection modification exception
            var selectedNodes = treeList1.Selection.ToArray();

            // Process all nodes without refreshing UI for each one (batch operation)
            foreach (var node in selectedNodes)
            {
                // Use centralized logic to handle unchecking - force to Unchecked state
                HandleCheckboxStateChange(node, CheckState.Unchecked, refreshUI: false);
            }

            // Refresh UI once after all changes are complete
            treeList1.RefreshDataSource();
        }



        private void delete_ItemClick(object sender, EventArgs e)
        {
            RemoveSearchItem();
        }

        private void NewEBaySearch_ItemClick(object sender, EventArgs e)
        {
            NewEbaySearch("New eBay Search");
            Analytics.AddEvent("", "KeywordAdded", 1);
        }

        private void NewSubSearch_ItemClick(object sender, EventArgs e)
        {
            NewChildTerm();
        }

        private void NewCopy_ItemClick(object sender, EventArgs e)
        {
            NewNodeCopy();
        }

        private void btnNewSearchQuery_Click(object sender, EventArgs e)
        {
            NewEbaySearch("New eBay Search");
            Analytics.AddEvent("", "KeywordAdded", 1);
        }

        private void repositoryItemButtonEditAlias_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            if (e.Button.Caption == "Add Sub Search")
                NewChildTerm();
        }

        private void treeList1_AfterCheckNode(object sender, NodeEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"AfterCheckNode: Node={e.Node.Id}, Visual={e.Node.CheckState}");

                // Prevent recursive calls when we're already updating checkboxes
                if (_isUpdatingCheckboxes)
                {
                    System.Diagnostics.Debug.WriteLine("AfterCheckNode: Skipping recursive call");
                    return;
                }

                // Get the current state from DATA MODEL, not visual state
                var currentState = GetDataModelCheckState(e.Node);
                var newState = GetNextCheckboxState(currentState);

                System.Diagnostics.Debug.WriteLine($"AfterCheckNode: DataModel={currentState}, New={newState}");

                // Handle the state change using centralized logic
                HandleCheckboxStateChange(e.Node, newState);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AfterCheckNode Exception: {ex.Message}");
            }
        }

        private void treeList1_AfterExpand(object sender, NodeEventArgs e)
        {
            // Update the data model to reflect the expansion state change
            var dataRecord = treeList1.GetDataRecordByNode(e.Node);
            if (dataRecord is Search.KeywordFolder folder)
            {
                folder.IsExpanded = true;
            }

            for (var j = 0; j < e.Node.Nodes.Count; j++)
            {
                SetNodeChecked(e.Node.Nodes[j]);
            }

            // No need to manually refresh - StateImageList automatically updates
            // when expansion state changes via GetStateImage event
        }

        private void treeList1_AfterCollapse(object sender, NodeEventArgs e)
        {
            // Update the data model to reflect the expansion state change
            var dataRecord = treeList1.GetDataRecordByNode(e.Node);
            if (dataRecord is Search.KeywordFolder folder)
            {
                folder.IsExpanded = false;
            }

            // No need to manually refresh - StateImageList automatically updates
            // when expansion state changes via GetStateImage event
        }

        private void treeList1_GetStateImage(object sender, GetStateImageEventArgs e)
        {
            var dataRecord = treeList1.GetDataRecordByNode(e.Node);
            if (dataRecord is Search.KeywordFolder)
            {
                // Set folder icon based on expansion state
                e.NodeImageIndex = e.Node.Expanded ? 1 : 0; // 0 = folder, 1 = folderopen
            }
            else if (dataRecord is Keyword2Find || dataRecord is ChildTerm)
            {
                // Set search icon for search terms (keywords and child terms)
                e.NodeImageIndex = 2; // 2 = search
            }
            else
            {
                // No state image for other types
                e.NodeImageIndex = -1;
            }
        }


        private void treeList1_CustomNodeCellEdit(object sender, GetCustomNodeCellEditEventArgs e)
        {
            if (e.Node == null)
                return;

            // All items use the same repository item for the Enabled column
            // Folder icons are handled in CustomDrawNodeCell event

            // Use type-based detection instead of level-based logic
            var dataRecord = treeList1.GetDataRecordByNode(e.Node);

            switch (dataRecord)
            {
                case Search.KeywordFolder folder:
                    // Folders can edit Alias (for renaming), Enabled (for checkbox), and bulk-editable properties
                    switch (e.Column.FieldName)
                    {
                        case "Alias":
                            // Allow editing folder name
                            break;
                        case "Enabled":
                            // Allow checkbox for enabling/disabling folders
                            // Use default repository item (repositoryItemCheckEditEnabled)
                            break;
                        case "Keywords":
                            // Allow Keywords column to display child count for folders
                            // Use read-only text editor to show the count
                            e.RepositoryItem = repositoryItemEditTextData;
                            break;

                        // Bulk-editable columns for folders
                        case "Price Min":
                        case "Price Max":
                            e.RepositoryItem = repositoryItemEditTextData;
                            break;
                        case "Threads":
                            e.RepositoryItem = repositoryItemEditTextData;
                            break;
                        case "Search in description":
                            // Use checkbox for boolean values
                            break;
                        case "Located in":
                            e.RepositoryItem = repositoryItemComboBoxLocatedIn;
                            break;
                        case "Ships to":
                            e.RepositoryItem = repositoryItemComboBoxShipsTo;
                            break;
                        case "Site":
                            // Use the same site dropdown as keywords
                            e.RepositoryItem = repositoryItemComboBoxSite;
                            break;
                        case "Category ID":
                            // Use the same popup container editor as keywords for category selection
                            // This will trigger repositoryItemPopupContainerEditCategory_Popup for bulk editing
                            break;
                        case "Condition":
                            // Use the same checked combo box as keywords for condition selection
                            e.RepositoryItem = repositoryItemCheckedComboBoxEditCondition;
                            break;
                        case "Seller type":
                            // Use the same combo box as keywords for seller type selection
                            e.RepositoryItem = repositoryItemComboBoxSellerType;
                            break;
                        case "Interval":
                            // Use the same time span editor as keywords for interval selection
                            e.RepositoryItem = repositoryIntervalEdit;
                            break;
                        case "View":
                            // Use the same MRU editor as keywords for view selection
                            e.RepositoryItem = repositoryItemViews;
                            break;
                        case "ListingType":
                            // Use the same checked combo box as keywords for listing type selection
                            e.RepositoryItem = repositoryItemCheckedComboBoxEditListingType;
                            break;
                        case "Sellers":
                        case "Ship Zipcode":
                            e.RepositoryItem = repositoryItemEditTextData;
                            break;

                        default:
                            // Disable editing for all other columns
                            e.RepositoryItem = repositoryItemEmpty4Filter;
                            break;
                    }
                    break;

                case Keyword2Find keyword:
                    // Keywords can edit all their properties
                    switch (e.Column.FieldName)
                    {
                        case "Alias":
                            break;

                        case "Type":
                            break;

                        case "Keywords":
                        case "Price Min":
                        case "Price Max":
                        case "Zip":
                            e.RepositoryItem = repositoryItemEditTextData;
                            break;

                        case "Category ID":
                            break;

                        case "Site":
                            e.RepositoryItem = repositoryItemComboBoxSite;
                            break;

                        case "Located in":
                            e.RepositoryItem = repositoryItemComboBoxLocatedIn;
                            break;

                        case "Ships to":
                            e.RepositoryItem = repositoryItemComboBoxShipsTo;
                            break;

                        case "Filter":
                            e.RepositoryItem = repositoryItemEmpty4Filter;
                            break;
                    }
                    break;

                case ChildTerm childTerm:
                    // ChildTerms have limited editing capabilities
                    switch (e.Column.FieldName)
                    {
                        case "Alias":
                        case "Keywords":
                        case "Price Min":
                        case "Price Max":
                            e.RepositoryItem = repositoryItemEditTextData;
                            break;

                        case "Category ID":
                            break;

                        case "Located in":
                        case "Site":
                        case "Ships to":
                        case "Zip":
                        case "Sellers":
                        case "Seller type":
                        case "Interval":
                        case "Threads":
                        case "View":
                        case "ListingType":
                            e.RepositoryItem = repositoryItemEmpty4Filter;
                            break;

                        case "Filter":
                            e.RepositoryItem = repositoryItemPopupContainerEditFilter;
                            break;
                    }
                    break;
            }
        }

        private void treeList1_InvalidNodeException(object sender, InvalidNodeExceptionEventArgs e)
        {
            // Log the exception for debugging purposes
            System.Diagnostics.Debug.WriteLine($"TreeList InvalidNodeException: {e.Exception.Message}");

            // Suppress the exception to prevent crashes
            e.ExceptionMode = ExceptionMode.NoAction;
        }

        private void treeList1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Insert && e.Modifiers == Keys.None)
            {
                e.Handled = true;
                NewEbaySearch("New eBay Search");
                Analytics.AddEvent("", "KeywordAdded", 1);
            }

            if (e.KeyCode == Keys.Insert && e.Modifiers == Keys.Control)
            {
                e.Handled = true;
                NewChildTerm();
            }

            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.None)
            {
                e.Handled = true;
                RemoveSearchItem();
            }

            if (e.KeyCode == Keys.F2 || e.KeyCode == Keys.Enter)
            {
                e.Handled = true;
                UserSettings.CanShowEbaySearchEditor = true;
                treeList1.ShowEditor();
                switch (treeList1.FocusedColumn.FieldName)
                {
                    case "Located in":
                    case "Ships to":
                    case "Site":
                        var editor = treeList1.ActiveEditor as ComboBoxEdit;
                        editor?.ShowPopup();

                        break;
                    case "Category ID":
                        var categoryEditor = treeList1.ActiveEditor as PopupContainerEdit;
                        categoryEditor?.ShowPopup();

                        break;

                    case "Condition":
                    case "ListingType":
                        var ceditor = treeList1.ActiveEditor as CheckedComboBoxEdit;
                        ceditor?.ShowPopup();

                        break;

                    case "View":
                        var veditor = treeList1.ActiveEditor as MRUEdit;
                        veditor?.ShowPopup();

                        break;
                    case "Filter":
                        var peditor = treeList1.ActiveEditor as PopupContainerEdit;
                        peditor?.ShowPopup();

                        break;
                }
            }
        }

        private void treeList1_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            UserSettings.CanShowEbaySearchEditor = true;
            treeList1.ShowEditor();
        }

        private void treeList1_MouseDown(object sender, MouseEventArgs e)
        {
            if (sender is TreeList tree)
            {
                var hitInfo = tree.CalcHitInfo(e.Location);
                if (e.Button == MouseButtons.Right && hitInfo.HitInfoType == HitInfoType.Cell)
                {
                    if (tree.Selection.Count > 1)
                    {
                        hitInfo.Node.Selected = true;

                        return;
                    }

                    tree.Selection.Clear();
                    hitInfo.Node.Selected = true;
                }
            }

            _pressedHitInfo = treeList1.CalcHitInfo(e.Location);
            // Count top-level items (folders and keywords at root level)
            var topLevelCount = treeList1.Nodes.Count(n =>
                treeList1.GetDataRecordByNode(n) is Search.KeywordFolder ||
                (treeList1.GetDataRecordByNode(n) is Keyword2Find && n.ParentNode == null));

            if (e.Button == MouseButtons.Left && treeList1.Selection.Count == topLevelCount)
            {
                while (treeList1.Selection.Count > 0)
                    treeList1.Selection.First().Selected = false;
            }

            if (_pressedHitInfo.Node != null)
            {
                var ri = treeList1.ViewInfo.GetRowInfoByPoint(_pressedHitInfo.MousePoint);

                if (ri == null || ri.Cells == null)
                    return;

                CellInfo cellInfo = null;
                foreach (var cell in ri.Cells)
                {
                    if (cell.Bounds.Contains(_pressedHitInfo.MousePoint))
                        cellInfo = cell;
                }

                if (cellInfo != null)
                {
                    var vi = cellInfo.EditorViewInfo as ButtonEditViewInfo;
                    var buttonInfo = vi?.ButtonInfoByPoint(_pressedHitInfo.MousePoint);
                    if (buttonInfo != null)
                    {
                        if (buttonInfo.Bounds.Contains(_pressedHitInfo.MousePoint))
                        {
                            UserSettings.CanShowEbaySearchEditor = true;

                            //_showButtons = true;
                            return;
                        }
                    }

                    if (cellInfo.EditorViewInfo is CheckEditViewInfo
                        || cellInfo.Column.Name == "cEnabled"
                        )
                    {
                        UserSettings.CanShowEbaySearchEditor = true;

                        return;
                    }
                }

                if (cellInfo != null && cellInfo.Focused)
                {
                    // Cancel the editor if the cell is not focused
                    UserSettings.CanShowEbaySearchEditor = true;
                    return;
                }
            }

            UserSettings.CanShowEbaySearchEditor = false;
        }

        private void treeList1_NodeCellStyle(object sender, GetCustomNodeCellStyleEventArgs e)
        {
            var dataRecord = treeList1.GetDataRecordByNode(e.Node);

            // Apply special styling for folders
            if (dataRecord is Search.KeywordFolder)
            {
                // Make folder names larger font size in addition to bold
                var currentFont = e.Appearance.Font ?? treeList1.Font;
                var folderFont = new Font(currentFont.FontFamily, currentFont.Size + 1, FontStyle.Bold);
                e.Appearance.Font = folderFont;
                e.Appearance.ForeColor = Color.DarkBlue;

                // Apply different background colors for bulk-editable vs non-editable columns
                if (IsBulkEditableColumn(e.Column.FieldName))
                {
                    // Slightly different background for bulk-editable columns
                    e.Appearance.BackColor = Color.FromArgb(240, 248, 255); // Light blue tint for editable

                    // Add subtle border to indicate editability
                    e.Appearance.BorderColor = Color.FromArgb(200, 220, 240);
                    e.Appearance.Options.UseBorderColor = true;
                }
                else
                {
                    // Standard folder background for non-editable columns
                    e.Appearance.BackColor = Color.FromArgb(248, 250, 255); // Very light blue tint

                    // Remove borders for non-editable columns
                    e.Appearance.BorderColor = Color.Transparent;
                    e.Appearance.Options.UseBorderColor = true;
                }
            }
            // Apply styling for child terms (non-keyword, non-folder items)
            else if (dataRecord is ChildTerm
                && e.Column.FieldName != "Alias"
                && e.Column.FieldName != "Keywords"
                && e.Column.FieldName != "Price Min"
                && e.Column.FieldName != "Price Max"
                && e.Column.FieldName != "Search in description"
                && e.Column.FieldName != "Category ID"
                && e.Column.FieldName != "Condition"
                && e.Column.FieldName != "Filter")
            {
                var skin = CommonSkins.GetSkin(defaultLookAndFeel1.LookAndFeel);
                var skinElement = skin[CommonSkins.SkinScrollButton];
                if (skinElement != null)
                {
                    e.Appearance.BackColor = skinElement.Color.BackColor;
                }
            }

            // Apply special background color for Filter column on keywords (not folders or child terms)
            if (dataRecord is Keyword2Find && e.Column.FieldName == "Filter")
            {
                e.Appearance.BackColor = Color.GhostWhite;
            }

            // Handle validation errors with special styling
            if (dataRecord is Keyword2Find keyword && keyword.HasValidationErrors)
            {
                // Use red text color for keywords with validation errors
                e.Appearance.ForeColor = Color.DarkRed;

                // Use italic and strikethrough to indicate error state
                var currentFont = e.Appearance.Font ?? treeList1.Font;
                var errorFont = new Font(currentFont, currentFont.Style | FontStyle.Italic | FontStyle.Strikeout);
                e.Appearance.Font = errorFont;

                // Light red background for validation errors
                e.Appearance.BackColor = Color.FromArgb(255, 240, 240);
            }
            // Handle enabled/disabled visual state - this should be the final styling decision
            else if (!e.Node.Checked)
            {
                // Use italic text for disabled/inactive keywords
                if (dataRecord is Keyword2Find || dataRecord is ChildTerm)
                {
                    var currentFont = e.Appearance.Font ?? treeList1.Font;
                    var italicFont = new Font(currentFont, currentFont.Style | FontStyle.Italic);
                    e.Appearance.Font = italicFont;
                }

                if (e.Node.Selected)
                {
                    // Selected but disabled - use darker grey to show selection
                    var skin = CommonSkins.GetSkin(defaultLookAndFeel1.LookAndFeel);
                    var skinElement = skin[CommonSkins.SkinGroupPanel];
                    if (skinElement != null)
                    {
                        var color = skinElement.Color.BackColor;
                        e.Appearance.BackColor = ControlPaint.Dark(color, -0.3f);
                    }
                }
                else
                {
                    // Disabled and not selected - use light grey
                    var skin = CommonSkins.GetSkin(defaultLookAndFeel1.LookAndFeel);
                    var skinElement = skin[CommonSkins.SkinGroupPanel];
                    if (skinElement != null)
                    {
                        var color = skinElement.Color.BackColor;
                        e.Appearance.BackColor = ControlPaint.Dark(color, -0.1f);

                    }
                }
            }
            // If checked (enabled), keep the default/existing background colors set above
        }

        private void treeList1_CalcNodeHeight(object sender, CalcNodeHeightEventArgs e)
        {
            var dataRecord = treeList1.GetDataRecordByNode(e.Node);

            // Apply row height variations (folders slightly taller than keywords)
            if (dataRecord is Search.KeywordFolder)
            {
                // Make folders 6 pixels taller than default
                e.NodeHeight = treeList1.RowHeight + 6;
            }
            else
            {
                // Keywords and child terms use default height
                e.NodeHeight = treeList1.RowHeight;
            }
        }

        private void treeList1_CustomDrawNodeCell(object sender, CustomDrawNodeCellEventArgs e)
        {
            //var dataRecord = treeList1.GetDataRecordByNode(e.Node);

            //// Draw thick left border for folders and thin left border for keywords
            //if (dataRecord is Search.KeywordFolder)
            //{
            //    // Draw thick left border for folders
            //    using (var pen = new Pen(Color.DarkBlue, 3))
            //    {
            //        e.Graphics.DrawLine(pen, e.Bounds.Left, e.Bounds.Top, e.Bounds.Left, e.Bounds.Bottom);
            //    }

            //    // Remove vertical borders by not drawing them (handled by appearance settings)
            //}
            //else if (dataRecord is Keyword2Find)
            //{
            //    // Draw thin left border for keywords
            //    using (var pen = new Pen(Color.Gray, 1))
            //    {
            //        e.Graphics.DrawLine(pen, e.Bounds.Left, e.Bounds.Top, e.Bounds.Left, e.Bounds.Bottom);
            //    }
            //}
        }

        private void treeList1_NodeChanged(object sender, NodeChangedEventArgs e)
        {
            if (e.ChangeType == NodeChangeTypeEnum.Add)
            {
                var dataRecord = treeList1.GetDataRecordByNode(e.Node);
                switch (dataRecord)
                {
                    case ChildTerm childTerm:
                        e.Node.Checked = childTerm.Enabled;
                        break;
                    case Keyword2Find keyword:
                        e.Node.CheckState = keyword.KeywordEnabled;
                        break;
                    case Search.KeywordFolder folder:
                        e.Node.Expanded = folder.IsExpanded;
                        // Set the folder's CheckState based on its children's states
                        e.Node.CheckState = CalculateFolderCheckState1(folder);
                        break;
                }
            }
        }

        private void treeList1_Paint(object sender, PaintEventArgs e)
        {
            foreach (var node in treeList1.Selection)
            {
                var rowInfo = treeList1.ViewInfo?.RowsInfo[node];

                if (rowInfo != null)
                {
                    var pen = new Pen(Color.Black, 2);
                    var pen2 = new Pen(Color.DeepSkyBlue, 3);
                    if (treeList1.FocusedColumn != null && rowInfo.Cells.Count > treeList1.FocusedColumn.VisibleIndex && treeList1.FocusedColumn.VisibleIndex > -1)
                    {
                        //e.Graphics.DrawRectangle(pen2, ((CellInfo)rowInfo.Cells[treeList1.FocusedColumn.ColumnIndex]).Bounds);
                        e.Graphics.DrawRectangle(pen2, rowInfo.Cells[treeList1.FocusedColumn.VisibleIndex].Bounds);
                    }

                    e.Graphics.DrawRectangle(pen, rowInfo.TotalBounds);
                }
            }
        }

        private void treeList1_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
        {
            try
            {
                if (e.Menu is TreeListNodeMenu nodeMenu && !treeList1.HasColumnErrors)
                {
                    if (nodeMenu.Node == null)
                        return;

                    treeList1.SetFocusedNode(nodeMenu.Node);

                    var dataRecord = treeList1.GetDataRecordByNode(nodeMenu.Node);

                    // Add context-specific menu items based on node type
                    switch (dataRecord)
                    {
                        case Search.KeywordFolder folder:
                            AddFolderMenuItems(nodeMenu, folder);
                            break;
                        case Keyword2Find keyword:
                            AddKeywordMenuItems(nodeMenu, keyword);
                            break;
                        case ChildTerm childTerm:
                            AddChildTermMenuItems(nodeMenu, childTerm);
                            break;
                        default:
                            // Fallback to original logic for unknown types
                            var dXMenuItemNewEBaySearch = new DXMenuItem("Add eBay Search (Insert)", NewEBaySearch_ItemClick);
                            nodeMenu.Items.Add(dXMenuItemNewEBaySearch);
                            break;
                    }

                    var menuCheckSelected = new DXMenuItem("Enable Selected", CheckSelected_ItemClick);
                    menuCheckSelected.SvgImage = Properties.Resources.checkbox_check;
                    menuCheckSelected.BeginGroup = true;
                    nodeMenu.Items.Add(menuCheckSelected);
                    var menuUnCheckSelected = new DXMenuItem("Disable Selected", UnCheckSelected_ItemClick);
                    menuUnCheckSelected.SvgImage = Properties.Resources.search_uncheck;
                    nodeMenu.Items.Add(menuUnCheckSelected);

                    // Add special menu items for OutOfStock keywords
                    if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find kw)
                    {
                        if (kw.ListingType.Contains(ListingType.OutOfStock))
                        {
                            menuCheckSelected.BeginGroup = true;
                            var dXMenuItemAddFromWatchList = new DXMenuItem("Import and replace ItemIDs from Ebay Watchlist",
                                AddFromWatchList_ItemClick);
                            nodeMenu.Items.Add(dXMenuItemAddFromWatchList);
                        }
                    }

                    var menuDelete = new DXMenuItem("Delete [" + treeList1.Selection.Count + "] " + "item(s)", delete_ItemClick);
                    menuDelete.SvgImage = Properties.Resources.Remove;
                    menuDelete.BeginGroup = true;
                    nodeMenu.Items.Add(menuDelete);
                }

                if (e.Menu is TreeListColumnMenu)
                {
                    var height = ((TreeList)sender).RowHeight;
                    var firstNode = treeList1.ViewInfo.RowsInfo.FirstOrDefault();

                    //// --- Add Fetch Search Results Item ---
                    //var menuItemFetchSearchResults = new DXMenuItem("Fetch Search Results", OnFetchSearchResultsTreelistClick);
                    //// Optional: Assign an SvgImage if available
                    //// menuItemFetchSearchResults.SvgImage = Resources.SearchIcon;
                    //menuItemFetchSearchResults.Tag = nodeMenu.Node; // Pass the node context
                    //menuItemFetchSearchResults.BeginGroup = true;
                    //nodeMenu.Items.Add(menuItemFetchSearchResults);
                    //// --- End Fetch Search Results Item ---

                    if (firstNode != null)
                        height = firstNode.Bounds.Height;

                    var nodeDecreaseHeight = new DXMenuItem("-Row height", (o, args) => ((TreeList)sender).RowHeight = height - 5);
                    e.Menu.Items.Add(nodeDecreaseHeight);
                    var nodeIncreaseHeight = new DXMenuItem("+Row height", (o, args) => ((TreeList)sender).RowHeight = height + 5);
                    e.Menu.Items.Add(nodeIncreaseHeight);
                }

                if (e.Menu is TreeListMenu treelistMenu && treelistMenu.GetType() == typeof(TreeListMenu))
                {
                    // Handle empty space right-click - show menu to create root folder
                    AddEmptySpaceMenuItems(treelistMenu);
                }
            }
            catch (Exception ex)
            {
                if (ProgramState.Isdebug)
                {
                    XtraMessageBox.Show("treeList1_PopupMenuShowing: " + ex.Message);
                }

                Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
            }
        }

        private void OnFetchSearchResultsTreelistClick(object sender, EventArgs e)
        {
            // Placeholder for future implementation for the treelist
            // Currently does nothing as requested.
            var menuItem = (DXMenuItem)sender;
            if (menuItem.Tag is TreeListNode node)
            {
                object dataRecord = treeList1.GetDataRecordByNode(node);
                if (dataRecord is Keyword2Find kw)
                {
                    // Example: Log or show a message for Keyword2Find
                    // Log?.Info($"Fetch Search Results clicked for Keyword: {kw.Alias}");
                    // XtraMessageBox.Show($"Fetch Search Results for '{kw.Alias}' is not yet implemented.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (dataRecord is ChildTerm ct)
                {
                    // Example: Log or show a message for ChildTerm
                    // Log?.Info($"Fetch Search Results clicked for Child Term: {ct.Alias}");
                    // XtraMessageBox.Show($"Fetch Search Results for '{ct.Alias}' is not yet implemented.", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }


        private void AddFromWatchList_ItemClick(object sender, EventArgs e)
        {
            if (treeList1.FocusedNode is { Level: 0 })
                if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find kw)
                {
                    if (kw.ListingType.Contains(ListingType.OutOfStock))
                    {
                        var ebayAccount = Form1.EBayAccountsList.FirstOrDefault();
                        if (ebayAccount == null)
                        {
                            XtraMessageBox.Show("No ebay accounts found. Please add your eBay account to uBuyFirst.");
                            return;
                        }

                        var apiContext =
                            ConnectionConfig.GetApiContextPlaceOffer(kw.EBaySite.SiteCode, ebayAccount.TokenPo);
                        var itemIds = ApiService.GetAllWatchlistItemIds(apiContext);

                        XtraMessageBox.Show("Imported " + itemIds.Count + " new itemIDs from Ebay Watchlist.\r\nPlease, click Stop/Start to restart your search.");
                        kw.Kws = string.Join(",", itemIds);
                        treeList1.RefreshDataSource();
                    }
                }
        }

        private void treeList1_ShowingEditor(object sender, CancelEventArgs e)
        {
            if (!UserSettings.CanShowEbaySearchEditor)
            {
                e.Cancel = true;

                return;
            }

            if (repositoryItemButtonNewKeyword.TextEditStyle == TextEditStyles.HideTextEditor)
                e.Cancel = true;

            // Prevent editing Keywords column for folders (it should only display the count)
            if (sender is TreeList treeList && treeList.FocusedNode != null && treeList.FocusedColumn?.FieldName == "Keywords")
            {
                var dataRecord = treeList.GetDataRecordByNode(treeList.FocusedNode);
                if (dataRecord is Search.KeywordFolder)
                {
                    e.Cancel = true;
                    return;
                }
            }

            var currentEditor = (sender as TreeList)?.ActiveEditor;

            if (currentEditor != null)
                currentEditor.IsModified = true;

            if (ProgramState.Isdebug)
            {
                /*
                                if (treeList1.Selection.Count > 0)
                                {
                                    var node = treeList1.Selection[0];
                                    if (node.Level == 0)
                                    {
                                        if (treeList1.GetDataRecordByNode(node) is Keyword2Find kw) Text = kw.Alias;
                                    }
                                    else
                                    {
                                        var childTerm = treeList1.GetDataRecordByNode(node) as ChildTerm;
                                        var keyword2Find = childTerm.GetParent();
                                        var isnull = "";
                                        if (keyword2Find == null)
                                            isnull = "NULL";
                                        Text = isnull + "\t" + childTerm.Alias;
                                    }
                                    if (node.ParentNode != null)
                                        Text = node.Id + "|" + node.ParentNode.Nodes.IndexOf(node);
                                }
                                */
            }
        }

        private void treeList1_ShownEditor(object sender, EventArgs e)
        {
            var treeList = sender as TreeList;
            var currentEditor = treeList?.ActiveEditor;
            if (currentEditor != null)
            {
                currentEditor.IsModified = true;
            }

            UserSettings.CanShowEbaySearchEditor = false;
        }

        private void treeList1_ValidateNode(object sender, ValidateNodeEventArgs e)
        {
            var node = e.Node;

            if (node == null)
                return;

            var dataRecord = treeList1.GetDataRecordByNode(node);
            switch (dataRecord)
            {
                case Keyword2Find kw:
                    var uniqueAliasesDict = Helpers.CountStrings(_ebaySearches.ChildrenCore.Select(k => k.Alias).ToList());
                    e.Valid = ValidateKeyword2Find(kw, out _, uniqueAliasesDict);
                    treeList1.RefreshCell(e.Node, e.Node.TreeList.Columns["ListingType"]);
                    treeList1.RefreshCell(e.Node, e.Node.TreeList.Columns["Alias"]);
                    break;
                case ChildTerm kw:
                    {
                        if (string.IsNullOrEmpty(kw.Alias))
                        {
                            e.Valid = false;
                            treeList1.SetColumnError(cAlias, $"Alias field should not be empty. [{kw.Keywords}]");
                        }

                        if (string.IsNullOrEmpty(kw.Keywords))
                        {
                            e.Valid = false;
                            treeList1.SetColumnError(cKeywords, $"Keywords field should not be empty. [{kw.Alias}]");
                        }

                        var priceMin = kw.PriceMin;
                        var priceMax = kw.PriceMax;
                        if (priceMin < 0.01 || priceMin > 10000000)
                        {
                            e.Valid = false;
                            treeList1.SetColumnError(cPriceMin, $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]");
                        }

                        if (priceMax < 0.01 || priceMax > 10000000)
                        {
                            e.Valid = false;
                            treeList1.SetColumnError(cPriceMax, $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]");
                        }

                        if (kw.CategoryIDs.Length > 20)
                        {
                            e.Valid = false;
                            treeList1.SetColumnError(cCategoryID, $"Maximum number of categories for Sub Search is 20. [{kw.Alias}]");
                        }

                        if (!Regex.IsMatch(string.Join(",", kw.CategoryIDs), "^[0-9,\\s]*$", RegexOptions.None))
                        {
                            e.Valid = false;
                            treeList1.SetColumnError(cCategoryID, $"Category field should be empty OR contain only comma separated numbers. [{kw.Alias}]");
                        }
                    }
                    break;
                case Search.KeywordFolder folder:
                    // Validate folder name
                    if (string.IsNullOrEmpty(folder.Name))
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cAlias, "Folder name should not be empty.");
                    }
                    // Check for duplicate names among siblings
                    else if (_ebaySearches.IsFolderNameTakenBySiblings(folder, folder.Name))
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cAlias, "A folder with this name already exists at this level. Please choose a different name.");

                        // Show message box to user
                        XtraMessageBox.Show(
                            $"A folder with the name '{folder.Name}' already exists at this level.\n\nPlease choose a different name.",
                            "Duplicate Folder Name",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Warning);
                    }
                    // Validate hierarchy
                    else if (!folder.ValidateHierarchy())
                    {
                        e.Valid = false;
                        treeList1.SetColumnError(cAlias, "Circular reference detected in folder hierarchy.");
                    }
                    break;
            }

            if (e.Valid)
            {
                treeList1.ClearColumnErrors();

                // Clear validation error state for keywords when validation passes
                if (treeList1.GetDataRecordByNode(e.Node) is Keyword2Find validKeyword)
                {
                    validKeyword.HasValidationErrors = false;
                    // Refresh the node to update visual styling
                    treeList1.RefreshNode(e.Node);
                }
            }
        }

        private bool ValidateKeyword2Find(Keyword2Find kw, out string errorMessages, Dictionary<string, int> existingAliases)
        {
            errorMessages = "";
            var eValid = true;
            if (string.IsNullOrEmpty(kw.Alias))
            {
                eValid = false;
                var errorMessage = $"Alias field should not be empty. [{kw.Kws}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cAlias, errorMessage);
            }
            else
            {
                kw.Alias = Helpers.MakeUniqAliasOnEdit(kw.Alias, existingAliases);
            }

            if (string.IsNullOrEmpty(kw.Kws))
            {
                if (LicenseUtility.CurrentLimits.SearchTermsCount != 10000)
                {
                    eValid = false;
                    var errorMessage = $"Keywords field should not be empty. \nWith Enterprise license you are able to leave Keywords field empty and search just by Category. [{kw.Alias}]";
                    errorMessages += errorMessage + "\r\n";
                    treeList1.SetColumnError(cKeywords, errorMessage);
                }

                if (string.IsNullOrEmpty(kw.Categories4Api))
                {
                    eValid = false;
                    var errorMessage = $"Please, fill either Keywords or Category field. [{kw.Alias}]";
                    errorMessages += errorMessage + "\r\n";
                    treeList1.SetColumnError(cKeywords, errorMessage);
                }

                if (kw.ListingType.Contains(ListingType.OutOfStock))
                {
                    eValid = false;
                    var errorMessage = $"Please, enter items IDs separated by commas [{kw.Alias}]";
                    errorMessages += errorMessage + "\r\n";
                    treeList1.SetColumnError(cKeywords, errorMessage);
                }
            }
            else
            {
                if (kw.ListingType.Contains(ListingType.OutOfStock))
                {
                    if (kw.Kws.Length > 10000 || kw.Kws.Length < 12)
                    {
                        eValid = false;
                        var errorMessage = $"Min/Max length for Out Of Stock Search is  12/3000. You have {kw.Kws.Length} [{kw.Alias}]";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }

                    var itemIDsPattern = "^((\\d{12})(,\\s*)*)*$";
                    if (!Regex.IsMatch(kw.Kws, itemIDsPattern))
                    {
                        eValid = false;
                        var errorMessage = $"Please, enter items IDs separated by commas [{kw.Alias}]";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }
                }
                else
                {
                    if (kw.Kws.Length > 350 || kw.Kws.Length < 2)
                    {
                        eValid = false;
                        var errorMessage = $"Min/Max length for eBay Search is  2/350. The maximum length for a single word is 98. [{kw.Alias}]";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }

                    if (kw.Kws.StartsWith("-"))
                    {
                        eValid = false;
                        var errorMessage = $"eBay Search cannot start with '-' symbol. [{kw.Alias}]";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }

                    var error = KeywordHelpers.ValidateLuceneKeyword(kw.Kws, kw.Categories4Api.Length > 0);
                    if (!string.IsNullOrEmpty(error))
                    {
                        eValid = false;
                        var errorMessage = $"[{kw.Alias}]\r\nKeywords field has incorrect value.\r\n"
                                           + "Common issues: \r\nNot matched parentheses.\r\nExtra commas.\r\n"
                                           + "Unsupported special chars like - \"(inches).\r\nCan't start with '*'.";
                        errorMessages += errorMessage + "\r\n";
                        treeList1.SetColumnError(cKeywords, errorMessage);
                    }
                }
            }

            if (kw.Categories4Api.Split(',').Length > 3)
            {
                eValid = false;
                var errorMessage = $"Maximum number of categories for eBay Search is 3. Best is to use 1 category. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cCategoryID, errorMessage);
            }

            //if (ConnectionConfig.BrowseAPIEnabled && !ConnectionConfig.TradingAPIEnabled && kw.Categories4Api.Split(',').Length > 1)
            //{
            //    eValid = false;
            //    var errorMessage = $"Maximum number of categories for eBay Search is 1. [{kw.Alias}]";
            //    errorMessages += errorMessage + "\r\n";
            //    treeList1.SetColumnError(cCategoryID, errorMessage);
            //}

            if (!Enum.IsDefined(typeof(CountryCodeType), kw.LocatedIn) && kw.LocatedIn != "Any")
            {
                eValid = false;
                var errorMessage = $"'Located in' value  [{kw.LocatedIn}] in search '{kw.Alias}' is invalid. Please, select a correct value.";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cCategoryID, errorMessage);
            }

            if (!Enum.IsDefined(typeof(CountryCodeType), kw.AvailableTo))
            {
                eValid = false;
                var errorMessage = $"'Available to' value [{kw.AvailableTo}] in search '{kw.Alias}' is invalid. Please, select a correct value.";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cCategoryID, errorMessage);
            }

            if (!Regex.IsMatch(kw.Categories4Api, "^[0-9,\\s]*$", RegexOptions.None))
            {
                eValid = false;
                var errorMessage = $"Category field should be empty OR contain only comma separated numbers. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cCategoryID, errorMessage);
            }

            var priceMin = kw.PriceMin;
            var priceMax = kw.PriceMax;
            if (priceMin < 0.01 || priceMin > 10000000)
            {
                eValid = false;
                var errorMessage = $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cPriceMin, errorMessage);
            }

            if (priceMax < 0.01 || priceMax > 10000000)
            {
                eValid = false;
                var errorMessage = $"Valid price range is 0.01 - 10,000,000. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cPriceMax, errorMessage);
            }

            if (kw.Sellers.Length > 10)
            {
                eValid = false;
                var errorMessage = $"Maximum number of sellers is 10. [{kw.Alias}]";
                errorMessages += errorMessage + "\r\n";
                treeList1.SetColumnError(cSellers, errorMessage);
            }

            // Set the validation error state on the keyword
            kw.HasValidationErrors = !eValid;

            return eValid;
        }

        #region KeywordDragDrop

        private void treeList1_DragDrop(object sender, DragEventArgs e)
        {
            if (sender is TreeList tl)
            {
                tl.ClearSorting();
                var p = tl.PointToClient(new Point(e.X, e.Y));
                var dragNode = e.Data.GetData(typeof(TreeListNode)) as TreeListNode;
                var targetNode = tl.CalcHitInfo(p).Node;

                if (dragNode == targetNode)
                    return;

                if (dragNode != null)
                {
                    var dragData = treeList1.GetDataRecordByNode(dragNode);

                    // Handle empty space drops (make root folder)
                    if (targetNode == null)
                    {
                        HandleEmptySpaceDrop(dragData);
                    }
                    // Handle existing node drops
                    else
                    {
                        var targetData = treeList1.GetDataRecordByNode(targetNode);

                        // Handle different drag & drop scenarios
                        switch (dragData)
                        {
                            case Search.KeywordFolder dragFolder:
                                HandleFolderDrop(dragFolder, targetData, targetNode);
                                break;
                            case Keyword2Find dragKeyword:
                                HandleKeywordDrop(dragKeyword, targetData, targetNode);
                                break;
                            case ChildTerm dragChildTerm:
                                HandleChildTermDrop(dragChildTerm, targetData, targetNode);
                                break;
                        }
                    }

                    treeList1.RefreshDataSource();
                    RefreshAllNodeStates();
                }
            }
        }

        private void treeList1_DragOver(object sender, DragEventArgs e)
        {
            var args = treeList1.GetDXDragEventArgs(e);
            if (args.Node == null)
            {
                if (args.HitInfo.HitInfoType == HitInfoType.Empty || args.TargetNode != null)
                    args.Effect = DragDropEffects.Copy;
                else
                    args.Effect = DragDropEffects.None;
                return;
            }

            if (args is { Node: not null, TargetNode: not null })
            {
                var dragData = treeList1.GetDataRecordByNode(args.Node);
                var targetData = treeList1.GetDataRecordByNode(args.TargetNode);

                // Determine if the drop operation is valid
                var canDrop = CanDropItem(dragData, targetData, args.DragInsertPosition);

                if (canDrop)
                {
                    args.Effect = DragDropEffects.Move;
                }
                else
                {
                    args.Effect = DragDropEffects.None;
                }
            }
            else
            {
                args.Effect = DragDropEffects.Move;
            }
        }

        #endregion KeywordDragDrop

        #region TreeList Filter Popup

        private void btnSavePopupFilter_Click(object sender, EventArgs e)
        {
            if (filterControlTerm.Tag is ChildTerm term)
            {
                term.SubSearch = new XFilterClassChild();
                term.SubSearch.Action = "Keep rows";
                term.SubSearch.Alias = term.Alias;
                term.SubSearch.Enabled = true;
                term.SubSearch.FilterCriteria = filterControlTerm.FilterCriteria;
                if (!filterControlTerm.IsFilterCriteriaValid)
                {
                    popupContainerControl1.OwnerEdit?.ClosePopup();

                    return;
                }

                term.SubSearch.Rebuild();
                filterControlTerm.Tag = term;
            }

            //filterControlTerm.FilterCriteria = null;
            filterControlTerm.Tag = null;
            popupContainerControl1.OwnerEdit?.ClosePopup();
        }

        private void treeList1_BeforeLoadLayout(object sender, LayoutAllowEventArgs e)
        {
            if (treeList1.OptionsLayout.LayoutVersion != e.PreviousVersion)
            {
                e.Allow = false;
            }
        }

        private void filterControlTerm_FilterChanged(object sender, FilterChangedEventArgs e)
        {
            if (e.Action == FilterChangedAction.FieldNameChange)
            {
                if (((ClauseNode)e.CurrentNode).FirstOperand.PropertyName == "Condition")
                {
                    if (e.CurrentNode is ClauseNode node)
                    {
                        node.Operation = ClauseType.AnyOf;
                    }
                }
            }

            repositoryItemPopupContainerEditFilter.Tag = filterControlTerm.FilterString;

        }

        private void repositoryItemPopupContainerEditFilter_QueryPopUp(object sender, CancelEventArgs e)
        {
            //filterControlTerm.MaxOperandsCount = 0;
            var term = treeList1.GetDataRecordByNode(treeList1.FocusedNode) as ChildTerm;
            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find)
            {
                e.Cancel = true;

                return;
            }

            if (term != null)
            {
                FilterAdapter1.DataSource = GridBuilder.DefaultDataTable; //Obsolete?
                //lblTermName.Text = term.Alias;
                //filterControlTerm.FilterCriteria = term.SubSearch?.FilterCriteria;
                filterControlTerm.FilterCriteria = ((PopupContainerEdit)sender).EditValue as CriteriaOperator;
                filterControlTerm.Tag = term;
                using var colBlob = filterControlTerm.FilterColumns.GetFilterColumnByCaption("Blob");
                if (colBlob != null)
                    filterControlTerm.FilterColumns.Remove(colBlob);

                using var colSource = filterControlTerm.FilterColumns.GetFilterColumnByCaption("Source");
                if (colSource != null)
                    filterControlTerm.FilterColumns.Remove(colSource);
            }
            else
            {
                filterControlTerm.FilterCriteria = null;
                filterControlTerm.Tag = null;
            }
        }

        #endregion TreeList Filter Popup

        private void RepositoryItemCheckedComboBoxEditListingType_EditValueChanged(object sender, EventArgs e)
        {
            var s = ((CheckedComboBoxEdit)sender).EditValue.ToString();
            if (s.Contains("OutOfStock") && s != "OutOfStock")
            {
                ((CheckedComboBoxEdit)sender).EditValue = "OutOfStock";
                XtraMessageBox.Show(
                    "Out of stock monitoring is enabled.\r\nPlease, enter your items IDs separated by comma into \"Keywords\" column.\r\nBuyItNow and Auction search have been disabled.");
            }

            var parent = ((CheckedComboBoxEdit)sender).Parent;
            ((TreeList)parent).PostEditor();
            ((TreeList)parent).Update();
            //((TreeList) parent).RefreshDataSource(); //
        }

        private bool _isUpdatingCheckboxes = false;
        private DateTime _lastCheckboxEventTime = DateTime.MinValue;
        private string _lastCheckboxNodeId = null;

        private void repositoryItemToggleSwitchEnabled_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"EditValueChanged: ENTRY - Flag={_isUpdatingCheckboxes}");

                // Prevent recursive calls when we're already updating checkboxes
                if (_isUpdatingCheckboxes)
                {
                    System.Diagnostics.Debug.WriteLine("EditValueChanged: Skipping recursive call");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"EditValueChanged: Sender={sender?.GetType().Name}");

                // Handle direct toggle switch clicks in the Enabled column
                if (sender is DevExpress.XtraEditors.ToggleSwitch toggleSwitch && toggleSwitch.Parent is TreeList treeList)
                {
                    var focusedNode = treeList.FocusedNode;
                    if (focusedNode != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"EditValueChanged: Node={focusedNode.Id}, ToggleSwitch.IsOn={toggleSwitch.IsOn}, EditValue={toggleSwitch.EditValue}");

                        // For toggle switch, the new state is directly determined by the IsOn property
                        CheckState newState = toggleSwitch.IsOn ? CheckState.Checked : CheckState.Unchecked;
                        System.Diagnostics.Debug.WriteLine($"EditValueChanged: ToggleSwitch IsOn={toggleSwitch.IsOn}, NewState={newState}");



                        // Handle the state change using centralized logic
                        HandleCheckboxStateChange(focusedNode, newState);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("EditValueChanged: No focused node");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("EditValueChanged: Sender is not CheckEdit or no TreeList parent");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"EditValueChanged Exception: {ex.Message}");
                _isUpdatingCheckboxes = false; // Reset flag on exception
            }
        }

        #region Context Menu Helpers

        private void AddFolderMenuItems(TreeListNodeMenu nodeMenu, Search.KeywordFolder folder)
        {
            var menuNewKeyword = new DXMenuItem("Add eBay Search", NewEBaySearch_ItemClick);
            menuNewKeyword.SvgImage = Properties.Resources.Add;
            menuNewKeyword.BeginGroup = true;
            nodeMenu.Items.Add(menuNewKeyword);

            var menuNewFolder = new DXMenuItem("New Folder", NewFolder_ItemClick);
            menuNewFolder.SvgImage = Properties.Resources.NewFolder;
            menuNewFolder.BeginGroup = true;
            nodeMenu.Items.Add(menuNewFolder);
        }

        private void AddKeywordMenuItems(TreeListNodeMenu nodeMenu, Keyword2Find keyword)
        {
            var dXMenuItemNewEBaySearch = new DXMenuItem("Add eBay Search (Insert)", NewEBaySearch_ItemClick);
            dXMenuItemNewEBaySearch.SvgImage = Properties.Resources.Add;
            nodeMenu.Items.Add(dXMenuItemNewEBaySearch);

            var dXMenuItem = new DXMenuItem("Duplicate eBay Search", NewCopy_ItemClick);
            dXMenuItem.SvgImage = Properties.Resources.duplicate;
            nodeMenu.Items.Add(dXMenuItem);

            var menuNewSubSearch = new DXMenuItem("New Sub Search (Insert)", NewSubSearch_ItemClick);
            menuNewSubSearch.SvgImage = Properties.Resources.add_task_list;
            nodeMenu.Items.Add(menuNewSubSearch);


            var menuNewFolder = new DXMenuItem("New Folder", NewFolderSameLevel_ItemClick);
            menuNewFolder.BeginGroup = true;
            menuNewFolder.SvgImage = Properties.Resources.NewFolder;
            nodeMenu.Items.Add(menuNewFolder);
        }

        private void AddChildTermMenuItems(TreeListNodeMenu nodeMenu, ChildTerm childTerm)
        {
            var menuNewSubSearch = new DXMenuItem("New Sub Search (Insert)", NewSubSearch_ItemClick);
            menuNewSubSearch.SvgImage = Properties.Resources.Add;
            nodeMenu.Items.Add(menuNewSubSearch);

            var dXMenuItem = new DXMenuItem("Duplicate Sub Search", NewCopy_ItemClick);
            dXMenuItem.SvgImage = Properties.Resources.duplicate;
            dXMenuItem.BeginGroup = true;
            nodeMenu.Items.Add(dXMenuItem);
        }

        private void AddEmptySpaceMenuItems(TreeListMenu nodeMenu)
        {
            var menuNewFolder = new DXMenuItem("New Folder", NewRootFolder_ItemClick);
            menuNewFolder.SvgImage = Properties.Resources.NewFolder;
            nodeMenu.Items.Add(menuNewFolder);
        }

        #endregion

        #region Drag & Drop Helpers

        private bool CanDropItem(object dragData, object targetData, DragInsertPosition position)
        {
            switch (dragData)
            {
                case Search.KeywordFolder dragFolder:
                    switch (targetData)
                    {
                        case Search.KeywordFolder targetFolder:
                            // Can drop folder into another folder (as child) or reorder folders
                            return !dragFolder.IsAncestorOf(targetFolder) && dragFolder != targetFolder;
                        case Keyword2Find:
                            // Can't drop folder on keyword
                            return false;
                        case ChildTerm:
                            // Can't drop folder on child term
                            return false;
                    }
                    break;
                case Keyword2Find:
                    switch (targetData)
                    {
                        case Search.KeywordFolder:
                            // Can drop keyword into folder
                            return position == DragInsertPosition.AsChild;
                        case Keyword2Find:
                            // Can reorder keywords
                            return position == DragInsertPosition.Before || position == DragInsertPosition.After;
                        case ChildTerm:
                            // Can't drop keyword on child term
                            return false;
                    }
                    break;
                case ChildTerm:
                    switch (targetData)
                    {
                        case Search.KeywordFolder:
                            // Can't drop child term on folder
                            return false;
                        case Keyword2Find:
                            // Can drop child term into keyword
                            return position == DragInsertPosition.AsChild;
                        case ChildTerm:
                            // Can reorder child terms
                            return position == DragInsertPosition.Before || position == DragInsertPosition.After;
                    }
                    break;
            }
            return false;
        }

        private void HandleFolderDrop(Search.KeywordFolder dragFolder, object targetData, TreeListNode targetNode)
        {
            switch (targetData)
            {
                case Search.KeywordFolder targetFolder:
                    // Move folder into another folder or reorder
                    if (dragFolder.ParentFolder != null)
                    {
                        dragFolder.ParentFolder.Children.Remove(dragFolder);
                    }
                    else
                    {
                        _ebaySearches.Folders.Remove(dragFolder);
                    }

                    dragFolder.ParentFolder = targetFolder;
                    targetFolder.Children.Add(dragFolder);
                    break;
            }
        }

        private void HandleKeywordDrop(Keyword2Find dragKeyword, object targetData, TreeListNode targetNode)
        {
            switch (targetData)
            {
                case Search.KeywordFolder targetFolder:
                    // Move keyword into folder
                    if (dragKeyword.ParentFolder != null)
                    {
                        dragKeyword.ParentFolder.Keywords.Remove(dragKeyword);
                    }

                    dragKeyword.ParentFolder = targetFolder;
                    targetFolder.Keywords.Add(dragKeyword);
                    break;
                case Keyword2Find targetKeyword:
                    // Reorder keywords within same parent
                    if (dragKeyword.ParentFolder == targetKeyword.ParentFolder)
                    {
                        var parentFolder = dragKeyword.ParentFolder;
                        if (parentFolder != null)
                        {
                            var dragIndex = parentFolder.Keywords.IndexOf(dragKeyword);
                            var targetIndex = parentFolder.Keywords.IndexOf(targetKeyword);

                            parentFolder.Keywords.RemoveAt(dragIndex);
                            parentFolder.Keywords.Insert(targetIndex, dragKeyword);
                        }
                    }
                    break;
            }
        }

        private void HandleChildTermDrop(ChildTerm dragChildTerm, object targetData, TreeListNode targetNode)
        {
            switch (targetData)
            {
                case Keyword2Find targetKeyword:
                    // Move child term to different keyword
                    var fromKeyword = dragChildTerm.GetParent();
                    if (fromKeyword != null)
                    {
                        dragChildTerm.RemoveFromParent(fromKeyword);
                        dragChildTerm.ChangeParent(targetKeyword);
                    }
                    break;
                case ChildTerm targetChildTerm:
                    // Reorder child terms within same keyword
                    var parentKeyword = dragChildTerm.GetParent();
                    var targetParentKeyword = targetChildTerm.GetParent();

                    if (parentKeyword == targetParentKeyword && parentKeyword != null)
                    {
                        var dragIndex = parentKeyword.ChildrenCore.IndexOf(dragChildTerm);
                        var targetIndex = parentKeyword.ChildrenCore.IndexOf(targetChildTerm);

                        parentKeyword.ChildrenCore.RemoveAt(dragIndex);
                        parentKeyword.ChildrenCore.Insert(targetIndex, dragChildTerm);
                    }
                    break;
            }
        }

        private void RefreshAllNodeStates()
        {
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
                RefreshNodeChildren(treeList1.Nodes[i]);
            }
        }

        private void RefreshNodeChildren(TreeListNode node)
        {
            for (var j = 0; j < node.Nodes.Count; j++)
            {
                SetNodeChecked(node.Nodes[j]);
                RefreshNodeChildren(node.Nodes[j]);
            }
        }

        private void HandleEmptySpaceDrop(object dragData)
        {
            switch (dragData)
            {
                case Search.KeywordFolder dragFolder:
                    // Only move if it's currently a subfolder
                    if (dragFolder.ParentFolder != null)
                    {
                        // Remove from current parent
                        dragFolder.ParentFolder.Children.Remove(dragFolder);

                        // Make it a root folder
                        dragFolder.ParentFolder = null;
                        _ebaySearches.Folders.Add(dragFolder);
                    }
                    // If already a root folder, do nothing (no-op)
                    break;

                // Keywords and ChildTerms cannot be moved to root level
                case Keyword2Find:
                case ChildTerm:
                    // Do nothing - these can't be root level items
                    break;
            }
        }

        #endregion

        #region Checkbox State Management

        /// <summary>
        /// Centralized method to handle checkbox state transitions for all interaction types
        /// </summary>
        private CheckState GetNextCheckboxState(CheckState currentState)
        {
            // Implement tristate cycling: Unchecked → Checked → Unchecked (skip Indeterminate for user clicks)
            // Indeterminate state is only set programmatically, not by user clicks
            switch (currentState)
            {
                case CheckState.Unchecked:
                    return CheckState.Checked;
                case CheckState.Checked:
                    return CheckState.Unchecked;
                case CheckState.Indeterminate:
                    // When user clicks indeterminate, cycle to checked
                    return CheckState.Checked;
                default:
                    return CheckState.Unchecked;
            }
        }

        /// <summary>
        /// Calculates the check state for a folder based on ALL descendants (keywords and child terms) recursively
        /// This implements the user's requested behavior: parent folder disabled only when ALL children recursively are deselected
        /// Only folders can have indeterminate state
        /// </summary>
        private CheckState CalculateFolderCheckState1(Search.KeywordFolder folder)
        {
            // Use the new recursive logic from the KeywordFolder class
            return folder.CalculateRecursiveFolderCheckState();
        }

        /// <summary>
        /// Gets the current check state from the data model, not the visual state
        /// </summary>
        private CheckState GetDataModelCheckState(TreeListNode node)
        {
            var dataRecord = treeList1.GetDataRecordByNode(node);
            switch (dataRecord)
            {
                case Keyword2Find kw:
                    return kw.KeywordEnabled;
                case ChildTerm childTerm:
                    return childTerm.Enabled ? CheckState.Checked : CheckState.Unchecked;
                case Search.KeywordFolder folder:
                    return CalculateFolderCheckState1(folder);
                default:
                    return CheckState.Unchecked;
            }
        }

        /// <summary>
        /// Handles checkbox state changes for any node type with proper tristate logic
        /// </summary>
        private void HandleCheckboxStateChange(TreeListNode node, CheckState newState, bool refreshUI = true)
        {
            var dataRecord = treeList1.GetDataRecordByNode(node);
            var foldersToUpdate = new HashSet<Search.KeywordFolder>();

            // Set flag to prevent recursive events
            _isUpdatingCheckboxes = true;
            try
            {
                switch (dataRecord)
                {
                    case Keyword2Find kw:
                        kw.KeywordEnabled = newState;
                        // Update visual state to match data model
                        node.CheckState = newState;
                        // Collect parent folder for batch update
                        if (kw.ParentFolder != null)
                            foldersToUpdate.Add(kw.ParentFolder);
                        break;

                    case ChildTerm childTerm:
                        var isChecked = newState == CheckState.Checked;
                        childTerm.Enabled = isChecked;
                        // Update visual state to match data model
                        node.Checked = isChecked;
                        // Collect parent folder for batch update
                        if (childTerm.GetParent()?.ParentFolder != null)
                            foldersToUpdate.Add(childTerm.GetParent().ParentFolder);
                        break;

                    case Search.KeywordFolder folder:
                        // Cascade enable/disable to all keywords and subfolders
                        CascadeFolderCheckState(folder, newState);
                        // Update visual state to match data model
                        node.CheckState = newState;
                        // Collect parent folder for batch update
                        if (folder.ParentFolder != null)
                            foldersToUpdate.Add(folder.ParentFolder);
                        break;
                }

                // Update all affected parent folders
                foreach (var folder in foldersToUpdate)
                {
                    UpdateParentFolderStates(folder);
                }
            }
            finally
            {
                _isUpdatingCheckboxes = false;
            }

            // Refresh the TreeList to show all visual updates (optional for batch operations)
            if (refreshUI)
            {
                treeList1.RefreshDataSource();
            }
        }

        #endregion

        #region Folder Check State Management

        /// <summary>
        /// Cascades folder check state to all keywords and subfolders
        /// </summary>
        private void CascadeFolderCheckState(Search.KeywordFolder folder, CheckState checkState)
        {
            // Ensure we never cascade Indeterminate state - convert to Checked if needed
            if (checkState == CheckState.Indeterminate)
                checkState = CheckState.Checked;

            // Temporarily set flag to prevent recursive events during visual state updates
            var wasUpdating = _isUpdatingCheckboxes;
            _isUpdatingCheckboxes = true;

            try
            {
                // Update all keywords in this folder
                foreach (var keyword in folder.Keywords)
                {
                    keyword.KeywordEnabled = checkState;

                    // Find and update the visual state for this keyword node
                    var keywordNode = FindNodeByDataRecord(keyword);
                    if (keywordNode != null)
                    {
                        keywordNode.CheckState = checkState;
                    }

                    // Also cascade to child terms (sub-searches) of each keyword
                    var isChecked = checkState == CheckState.Checked;
                    foreach (var childTerm in keyword.ChildrenCore)
                    {
                        childTerm.Enabled = isChecked;

                        // Find and update the visual state for this child term node
                        var childTermNode = FindNodeByDataRecord(childTerm);
                        if (childTermNode != null)
                        {
                            childTermNode.Checked = isChecked;
                        }
                    }
                }

                // Recursively update all subfolders and their contents
                foreach (var childFolder in folder.Children)
                {
                    CascadeFolderCheckState(childFolder, checkState);

                    // Find and update the visual state for this subfolder node
                    var subfolderNode = FindNodeByDataRecord(childFolder);
                    if (subfolderNode != null)
                    {
                        subfolderNode.CheckState = checkState;
                    }
                }
            }
            finally
            {
                // Restore the original flag state
                _isUpdatingCheckboxes = wasUpdating;
            }

            // Note: Don't refresh here - let the calling method handle batch refresh
        }

        /// <summary>
        /// Updates parent folder states based on their children's states (supports tristate)
        /// </summary>
        private void UpdateParentFolderStates(Search.KeywordFolder folder)
        {
            if (folder == null) return;

            // Find and update the folder's node using existing SetNodeChecked logic
            var folderNode = FindNodeByDataRecord(folder);
            if (folderNode != null)
            {
                // Use the existing SetNodeChecked method which has the correct tristate logic
                SetNodeChecked(folderNode);

                // Note: We don't call RefreshNode here to avoid format conversion errors
                // The visual refresh will be handled by the calling method using RefreshDataSource
            }

            // Recursively update parent folder
            if (folder.ParentFolder != null)
                UpdateParentFolderStates(folder.ParentFolder);
        }

        /// <summary>
        /// Finds a TreeListNode by its associated data record
        /// </summary>
        private TreeListNode FindNodeByDataRecord(object dataRecord)
        {
            for (int i = 0; i < treeList1.Nodes.Count; i++)
            {
                var node = FindNodeRecursive(treeList1.Nodes[i], dataRecord);
                if (node != null) return node;
            }
            return null;
        }

        /// <summary>
        /// Recursively searches for a node with the specified data record
        /// </summary>
        private TreeListNode FindNodeRecursive(TreeListNode node, object dataRecord)
        {
            if (treeList1.GetDataRecordByNode(node) == dataRecord)
                return node;

            foreach (TreeListNode childNode in node.Nodes)
            {
                var found = FindNodeRecursive(childNode, dataRecord);
                if (found != null) return found;
            }
            return null;
        }

        #endregion

        #region Folder Operations

        private void CreateNewFolder()
        {
            // Determine the parent folder
            Search.KeywordFolder parentFolder = null;
            var focusedNode = treeList1.FocusedNode;
            if (focusedNode != null)
            {
                var dataRecord = treeList1.GetDataRecordByNode(focusedNode);
                parentFolder = dataRecord switch
                {
                    Search.KeywordFolder folder => folder,
                    Keyword2Find kw2Find => kw2Find.ParentFolder,
                    _ => parentFolder,

                };
            }

            // Generate a unique folder name
            string uniqueName;
            if (parentFolder != null)
            {
                uniqueName = Search.KeywordFolder.GenerateUniqueName("New Folder", parentFolder.Children);
            }
            else
            {
                uniqueName = _ebaySearches.GenerateUniqueRootFolderName("New Folder");
            }

            // Create the new folder with unique name
            var newFolder = new Search.KeywordFolder
            {
                Name = uniqueName,
                Id = Guid.NewGuid().ToString(),
                ParentFolder = parentFolder
            };

            // Add to the appropriate parent
            if (parentFolder != null)
            {
                parentFolder.Children.Add(newFolder);
            }
            else
            {
                _ebaySearches.Folders.Add(newFolder);
            }

            treeList1.RefreshDataSource();
            treeList1.ExpandAll();
            RefreshAllNodeStates();

            // Start editing the new folder name immediately
            var newNode = FindNodeByDataRecord(newFolder);
            if (newNode != null)
            {
                treeList1.FocusedNode = newNode;
                treeList1.FocusedColumn = treeList1.Columns["Alias"];
                treeList1.ShowEditor();
            }
        }

        private void CreateNewFolderSameLevel()
        {
            // Determine the parent folder based on the focused keyword's parent
            Search.KeywordFolder parentFolder = null;
            var focusedNode = treeList1.FocusedNode;
            if (focusedNode != null)
            {
                var dataRecord = treeList1.GetDataRecordByNode(focusedNode);
                if (dataRecord is Keyword2Find keyword)
                {
                    // For keywords, use their parent folder as the parent for the new folder
                    parentFolder = keyword.ParentFolder;
                }
            }

            // Generate a unique folder name
            string uniqueName;
            if (parentFolder != null)
            {
                uniqueName = Search.KeywordFolder.GenerateUniqueName("New Folder", parentFolder.Children);
            }
            else
            {
                uniqueName = _ebaySearches.GenerateUniqueRootFolderName("New Folder");
            }

            // Create the new folder with unique name
            var newFolder = new Search.KeywordFolder
            {
                Name = uniqueName,
                Id = Guid.NewGuid().ToString(),
                ParentFolder = parentFolder
            };

            // Add to the appropriate parent
            if (parentFolder != null)
            {
                parentFolder.Children.Add(newFolder);
            }
            else
            {
                _ebaySearches.Folders.Add(newFolder);
            }

            treeList1.RefreshDataSource();
            treeList1.ExpandAll();
            RefreshAllNodeStates();

            // Start editing the new folder name immediately
            var newNode = FindNodeByDataRecord(newFolder);
            if (newNode != null)
            {
                treeList1.FocusedNode = newNode;
                treeList1.FocusedColumn = treeList1.Columns["Alias"];
                treeList1.ShowEditor();
            }
        }

        private void CreateNewRootFolder()
        {
            // Always create at root level, ignoring any selection
            var uniqueName = _ebaySearches.GenerateUniqueRootFolderName("New Folder");

            // Create the new folder with unique name at root level
            var newFolder = new Search.KeywordFolder
            {
                Name = uniqueName,
                Id = Guid.NewGuid().ToString(),
                ParentFolder = null // Always null for root folders
            };

            // Add to root folder collection
            _ebaySearches.Folders.Add(newFolder);

            treeList1.RefreshDataSource();
            treeList1.ExpandAll();
            RefreshAllNodeStates();

            // Start editing the new folder name immediately
            var newNode = FindNodeByDataRecord(newFolder);
            if (newNode != null)
            {
                treeList1.FocusedNode = newNode;
                treeList1.FocusedColumn = treeList1.Columns["Alias"];
                treeList1.ShowEditor();
            }
        }

        private void NewFolder_ItemClick(object sender, EventArgs e)
        {
            CreateNewFolder();
        }

        private void NewFolderSameLevel_ItemClick(object sender, EventArgs e)
        {
            CreateNewFolderSameLevel();
        }

        private void NewRootFolder_ItemClick(object sender, EventArgs e)
        {
            CreateNewRootFolder();
        }

        private void RenameFolder_ItemClick(object sender, EventArgs e)
        {
            var focusedNode = treeList1.FocusedNode;
            if (focusedNode != null && treeList1.GetDataRecordByNode(focusedNode) is Search.KeywordFolder folder)
            {
                // Start editing the folder name
                treeList1.FocusedColumn = treeList1.Columns["Alias"];
                treeList1.ShowEditor();
            }
        }



        // DeleteFolder_ItemClick method removed - folder deletion is now handled
        // by the unified RemoveSearchItem() method in Form1.EBaySearches.cs

        /// <summary>
        /// Determines if a column supports bulk editing for folders
        /// </summary>
        /// <param name="columnFieldName">The field name of the column</param>
        /// <returns>True if the column supports bulk editing for folders</returns>
        private bool IsBulkEditableColumn(string columnFieldName)
        {
            return columnFieldName switch
            {
                "Price Min" => true,
                "Price Max" => true,
                "Threads" => true,
                "Search in description" => true,
                "Located in" => true,
                "Ships to" => true,
                "Seller type" => true,
                "Site" => true,
                "View" => true,
                "Condition" => true,
                "Category ID" => true,
                "ListingType" => true,
                "Sellers" => true,
                "Ship Zipcode" => true,
                "Interval" => true,
                _ => false
            };
        }

        /// <summary>
        /// Maps property display names to TreeList column field names
        /// </summary>
        private string GetColumnFieldName(string propertyDisplayName)
        {
            return propertyDisplayName switch
            {
                "Listing Type" => "ListingType",
                "Category ID" => "Categories4Api",
                "Site" => "EbaySiteName",
                "View Name" => "ViewName",
                "Seller Type" => "SellerType",
                "Price Min" => "PriceMin",
                "Price Max" => "PriceMax",
                "Threads" => "Threads",
                "Search in Description" => "SearchInDescription",
                "Located in" => "LocatedIn",
                "Ships to" => "AvailableTo",
                "Ship Zipcode" => "Zip",
                "Condition" => "Condition",
                "Sellers" => "Sellers",
                "Interval" => "Frequency",
                _ => propertyDisplayName // Default to the same name
            };
        }

        /// <summary>
        /// Event handler for bulk changes applied to refresh TreeList
        /// </summary>
        private void OnBulkChangesApplied(object sender, Search.KeywordFolder.BulkChangesAppliedEventArgs e)
        {
            try
            {
                // Refresh the TreeList to show updated values
                treeList1.RefreshDataSource();

                // Find and refresh the specific folder node that was changed
                if (e.ChangedFolder != null)
                {
                    var node = treeList1.FindNodeByKeyID(e.ChangedFolder.Id);
                    if (node != null)
                    {
                        // Multiple refresh approaches to ensure the folder cell updates

                        // 1. Refresh the specific node to update its cell values
                        treeList1.RefreshNode(node);

                        // 2. Invalidate the node to force a visual refresh
                        treeList1.InvalidateNode(node);

                        // 3. Force refresh of specific column if we know which property changed
                        if (!string.IsNullOrEmpty(e.PropertyName))
                        {
                            // Map display names to actual field names
                            var fieldName = GetColumnFieldName(e.PropertyName);
                            var column = treeList1.Columns.ColumnByFieldName(fieldName);
                            if (column != null)
                            {
                                // Invalidate the specific cell
                                treeList1.InvalidateCell(node, column);
                            }
                        }

                        // 4. Final refresh to ensure everything is updated
                        treeList1.RefreshNode(node);
                    }
                }

                // Also refresh all node states to ensure visual consistency
                RefreshAllNodeStates();

                // Force a final refresh after a brief delay to ensure all changes are visible
                System.Windows.Forms.Application.DoEvents();
                treeList1.Refresh();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error refreshing TreeList after bulk changes: {ex.Message}");
            }
        }

        #endregion
    }
}
