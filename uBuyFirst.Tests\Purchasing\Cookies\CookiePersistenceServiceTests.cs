using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using uBuyFirst.Other;
using uBuyFirst.Purchasing.Cookies;

namespace uBuyFirst.Tests.Purchasing.Cookies
{
    [TestClass]
    public class CookiePersistenceServiceTests
    {
        private static readonly string TestCookieFilePath = Path.Combine(Folders.Settings, "cookies.dat");
        private CookieProfile _testProfile;

        [TestInitialize]
        public void Setup()
        {
            // Clean up any existing test files
            if (File.Exists(TestCookieFilePath))
            {
                File.Delete(TestCookieFilePath);
            }

            // Create test profile
            _testProfile = new CookieProfile
            {
                Name = "Test Profile",
                Profile = "test-profile-path"
            };

            // Ensure Folders.Settings directory exists
            if (!Directory.Exists(Folders.Settings))
            {
                Directory.CreateDirectory(Folders.Settings);
            }
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Clean up test files
            if (File.Exists(TestCookieFilePath))
            {
                File.Delete(TestCookieFilePath);
            }
        }

        [TestMethod]
        public async Task SaveCookiesAsync_WithValidCookies_SavesSuccessfully()
        {
            // Arrange
            var cookieContainer = CreateTestCookieContainer();
            var domainCache = new ConcurrentDictionary<string, CookieContainer>();
            domainCache.TryAdd(".ebay.com", cookieContainer);

            // Act
            await CookiePersistenceService.SaveCookiesAsync(domainCache, _testProfile);

            // Assert
            Assert.IsTrue(File.Exists(TestCookieFilePath), "Cookie file should be created");
        }

        [TestMethod]
        public async Task LoadCookiesAsync_WithValidSavedCookies_LoadsSuccessfully()
        {
            // Arrange
            var originalContainer = CreateTestCookieContainer();
            var domainCache = new ConcurrentDictionary<string, CookieContainer>();
            domainCache.TryAdd(".ebay.com", originalContainer);

            // Save cookies first
            await CookiePersistenceService.SaveCookiesAsync(domainCache, _testProfile);

            // Act
            var loadedCookies = await CookiePersistenceService.LoadCookiesAsync(new[] { ".ebay.com" }, _testProfile);

            // Assert
            Assert.IsNotNull(loadedCookies, "Loaded cookies should not be null");
            Assert.IsTrue(loadedCookies.ContainsKey(".ebay.com"), "Should contain the saved domain");
            
            var loadedContainer = loadedCookies[".ebay.com"];
            Assert.IsTrue(loadedContainer.Count > 0, "Loaded container should have cookies");
        }

        [TestMethod]
        public async Task LoadCookiesAsync_WithNonExistentFile_ReturnsNull()
        {
            // Act
            var result = await CookiePersistenceService.LoadCookiesAsync(new[] { ".ebay.com" }, _testProfile);

            // Assert
            Assert.IsNull(result, "Should return null when no file exists");
        }

        [TestMethod]
        public async Task LoadCookiesAsync_WithDifferentProfile_ReturnsNull()
        {
            // Arrange
            var cookieContainer = CreateTestCookieContainer();
            var domainCache = new ConcurrentDictionary<string, CookieContainer>();
            domainCache.TryAdd(".ebay.com", cookieContainer);

            // Save with original profile
            await CookiePersistenceService.SaveCookiesAsync(domainCache, _testProfile);

            // Create different profile
            var differentProfile = new CookieProfile
            {
                Name = "Different Profile",
                Profile = "different-profile-path"
            };

            // Act
            var result = await CookiePersistenceService.LoadCookiesAsync(new[] { ".ebay.com" }, differentProfile);

            // Assert
            Assert.IsNull(result, "Should return null when profile doesn't match");
            Assert.IsFalse(File.Exists(TestCookieFilePath), "File should be deleted after validation failure");
        }

        [TestMethod]
        public async Task LoadCookiesAsync_WithExpiredData_ReturnsNull()
        {
            // This test would require modifying the saved timestamp to be old
            // For now, we'll test the basic functionality
            // In a real scenario, you might use dependency injection to mock DateTime.UtcNow

            // Arrange
            var cookieContainer = CreateTestCookieContainer();
            var domainCache = new ConcurrentDictionary<string, CookieContainer>();
            domainCache.TryAdd(".ebay.com", cookieContainer);

            await CookiePersistenceService.SaveCookiesAsync(domainCache, _testProfile);

            // Act - Load immediately (should work)
            var result = await CookiePersistenceService.LoadCookiesAsync(new[] { ".ebay.com" }, _testProfile);

            // Assert
            Assert.IsNotNull(result, "Fresh cookies should load successfully");
        }

        [TestMethod]
        public void DeleteStoredCookies_WithExistingFile_DeletesFile()
        {
            // Arrange
            File.WriteAllText(TestCookieFilePath, "test content");
            Assert.IsTrue(File.Exists(TestCookieFilePath), "Test file should exist");

            // Act
            CookiePersistenceService.DeleteStoredCookies();

            // Assert
            Assert.IsFalse(File.Exists(TestCookieFilePath), "File should be deleted");
        }

        [TestMethod]
        public void DeleteStoredCookies_WithNonExistentFile_DoesNotThrow()
        {
            // Arrange
            Assert.IsFalse(File.Exists(TestCookieFilePath), "File should not exist");

            // Act & Assert
            CookiePersistenceService.DeleteStoredCookies(); // Should not throw
        }

        [TestMethod]
        public async Task SaveAndLoadCookies_WithMultipleDomains_WorksCorrectly()
        {
            // Arrange
            var ebayContainer = CreateTestCookieContainer("ebay-cookie", "ebay-value");
            var amazonContainer = CreateTestCookieContainer("amazon-cookie", "amazon-value");
            
            var domainCache = new ConcurrentDictionary<string, CookieContainer>();
            domainCache.TryAdd(".ebay.com", ebayContainer);
            domainCache.TryAdd(".amazon.com", amazonContainer);

            // Act
            await CookiePersistenceService.SaveCookiesAsync(domainCache, _testProfile);
            
            var loadedEbay = await CookiePersistenceService.LoadCookiesAsync(new[] { ".ebay.com" }, _testProfile);
            var loadedAmazon = await CookiePersistenceService.LoadCookiesAsync(new[] { ".amazon.com" }, _testProfile);

            // Assert
            Assert.IsNotNull(loadedEbay, "eBay cookies should load");
            Assert.IsNotNull(loadedAmazon, "Amazon cookies should load");
            
            Assert.IsTrue(loadedEbay.ContainsKey(".ebay.com"), "Should contain eBay domain");
            Assert.IsTrue(loadedAmazon.ContainsKey(".amazon.com"), "Should contain Amazon domain");
        }

        [TestMethod]
        public async Task SaveCookiesAsync_WithNullProfile_DoesNotSave()
        {
            // Arrange
            var cookieContainer = CreateTestCookieContainer();
            var domainCache = new ConcurrentDictionary<string, CookieContainer>();
            domainCache.TryAdd(".ebay.com", cookieContainer);

            // Act
            await CookiePersistenceService.SaveCookiesAsync(domainCache, null);

            // Assert
            Assert.IsFalse(File.Exists(TestCookieFilePath), "Should not save when profile is null");
        }

        [TestMethod]
        public async Task SaveCookiesAsync_WithEmptyProfile_DoesNotSave()
        {
            // Arrange
            var cookieContainer = CreateTestCookieContainer();
            var domainCache = new ConcurrentDictionary<string, CookieContainer>();
            domainCache.TryAdd(".ebay.com", cookieContainer);

            var emptyProfile = new CookieProfile { Profile = "" };

            // Act
            await CookiePersistenceService.SaveCookiesAsync(domainCache, emptyProfile);

            // Assert
            Assert.IsFalse(File.Exists(TestCookieFilePath), "Should not save when profile is empty");
        }

        /// <summary>
        /// Creates a test cookie container with sample cookies
        /// </summary>
        private CookieContainer CreateTestCookieContainer(string cookieName = "test-cookie", string cookieValue = "test-value")
        {
            var container = new CookieContainer { PerDomainCapacity = 100 };
            
            var cookie = new Cookie(cookieName, cookieValue, "/", ".ebay.com")
            {
                HttpOnly = true,
                Secure = true,
                Expires = DateTime.UtcNow.AddDays(1) // Valid for 1 day
            };
            
            container.Add(cookie);
            return container;
        }
    }
}
