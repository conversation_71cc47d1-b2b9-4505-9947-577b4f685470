﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Newtonsoft.Json;
using uBuyFirst.Other;

namespace uBuyFirst.Purchasing.Cookies
{
    /// <summary>
    /// Handles persistent storage of cookies to disk with encryption and validation
    /// </summary>
    public static class CookiePersistenceService
    {
        private static readonly string CookieFilePath = Path.Combine(Folders.Settings, "cookies.dat");
        private static readonly TimeSpan MaxCookieAge = TimeSpan.FromHours(144);

        /// <summary>
        /// Saves the current cookie cache to encrypted disk storage
        /// </summary>
        /// <param name="domainCookieCache">Current domain cookie cache</param>
        /// <param name="profile">Current Firefox profile</param>
        public static async Task SaveCookiesAsync(ConcurrentDictionary<string, CookieContainer> domainCookieCache, CookieProfile profile)
        {
            if (profile == null || string.IsNullOrEmpty(profile.Profile))
            {
                Debug.WriteLine("Cannot save cookies - no profile specified");
                return;
            }

            try
            {
                var persistentData = new PersistentCookieData
                {
                    ProfilePath = profile.Profile,
                    SavedAt = DateTime.UtcNow,
                    DomainContainers = new Dictionary<string, PersistentCookieContainer>()
                };

                // Convert each domain's cookies to persistent format
                foreach (var kvp in domainCookieCache)
                {
                    var persistentContainer = ConvertToPersistentContainer(kvp.Value, kvp.Key);
                    if (persistentContainer.Cookies.Any())
                    {
                        persistentData.DomainContainers[kvp.Key] = persistentContainer;
                    }
                }

                // Only save if we have cookies to persist
                if (persistentData.DomainContainers.Any())
                {
                    await SaveEncryptedDataAsync(persistentData);
                    Debug.WriteLine($"Saved {persistentData.DomainContainers.Count} domain cookie containers to disk");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving cookies to disk: {ex.Message}");
                // Don't throw - persistence failure shouldn't break main flow
            }
        }

        /// <summary>
        /// Loads cookies from disk for specific domains if valid
        /// </summary>
        /// <param name="hostNames">Domain names to load cookies for</param>
        /// <param name="currentProfile">Current Firefox profile for validation</param>
        /// <returns>Cookie containers for the domains, or null if invalid/missing</returns>
        public static async Task<ConcurrentDictionary<string, CookieContainer>> LoadCookiesAsync(IEnumerable<string> hostNames, CookieProfile currentProfile)
        {
            try
            {
                if (!File.Exists(CookieFilePath))
                {
                    Debug.WriteLine("No persistent cookie file found");
                    return null;
                }

                var persistentData = await LoadEncryptedDataAsync();
                if (persistentData == null)
                {
                    Debug.WriteLine("Failed to load persistent cookie data");
                    return null;
                }

                // Validate the loaded data
                if (!IsValidCookieData(persistentData, currentProfile))
                {
                    Debug.WriteLine("Persistent cookie data validation failed - deleting file");
                    DeleteStoredCookies();
                    return null;
                }

                // Convert back to CookieContainer format for requested domains
                var result = new ConcurrentDictionary<string, CookieContainer>();
                var hostNamesList = hostNames.ToList();
                var cacheKey = string.Join(",", hostNamesList.OrderBy(x => x));

                if (persistentData.DomainContainers.TryGetValue(cacheKey, out var persistentContainer))
                {
                    var cookieContainer = ConvertFromPersistentContainer(persistentContainer);
                    if (cookieContainer.Count > 0)
                    {
                        result.TryAdd(cacheKey, cookieContainer);
                        Debug.WriteLine($"Loaded {cookieContainer.Count} cookies from disk for domains: {cacheKey}");
                    }
                }

                return result.Any() ? result : null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading cookies from disk: {ex.Message}");
                DeleteStoredCookies(); // Clean up corrupted file
                return null;
            }
        }

        /// <summary>
        /// Deletes the stored cookie file from disk
        /// </summary>
        public static void DeleteStoredCookies()
        {
            try
            {
                if (File.Exists(CookieFilePath))
                {
                    File.Delete(CookieFilePath);
                    Debug.WriteLine("Deleted persistent cookie file");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error deleting persistent cookie file: {ex.Message}");
            }
        }

        /// <summary>
        /// Converts a CookieContainer to persistent storage format
        /// </summary>
        private static PersistentCookieContainer ConvertToPersistentContainer(CookieContainer container, string cacheKey)
        {
            var persistentContainer = new PersistentCookieContainer
            {
                CacheKey = cacheKey,
                SavedAt = DateTime.UtcNow,
                Cookies = new List<CookieData>()
            };

            // Extract cookies using domain-specific approach (more reliable)
            var extractedCookies = GetCookiesForCacheKey(container, cacheKey);

            // If no cookies found via domain-specific extraction, try reflection as fallback
            if (!extractedCookies.Any())
            {
                extractedCookies = GetAllCookies(container);
            }

            // Convert cookies to persistent format
            foreach (Cookie cookie in extractedCookies)
            {
                // Only save non-expired cookies
                if (cookie.Expires == DateTime.MinValue || cookie.Expires > DateTime.UtcNow)
                {
                    persistentContainer.Cookies.Add(new CookieData
                    {
                        Name = cookie.Name,
                        Value = cookie.Value,
                        Domain = cookie.Domain,
                        Path = cookie.Path,
                        Expires = cookie.Expires == DateTime.MinValue ? null : cookie.Expires,
                        HttpOnly = cookie.HttpOnly,
                        Secure = cookie.Secure
                    });
                }
            }

            Debug.WriteLine($"Converted {persistentContainer.Cookies.Count} cookies for cache key: {cacheKey}");
            return persistentContainer;
        }

        /// <summary>
        /// Gets cookies for specific domains based on cache key
        /// </summary>
        private static IEnumerable<Cookie> GetCookiesForCacheKey(CookieContainer container, string cacheKey)
        {
            var cookies = new List<Cookie>();

            try
            {
                Debug.WriteLine($"GetCookiesForCacheKey: Processing cache key '{cacheKey}' with container count {container.Count}");

                // Parse domains from cache key (format: ".ebay.com,.ebay.co.uk")
                var domains = cacheKey.Split(',').Select(d => d.Trim()).ToArray();
                Debug.WriteLine($"GetCookiesForCacheKey: Parsed {domains.Length} domains: {string.Join(", ", domains)}");

                foreach (var domain in domains)
                {
                    try
                    {
                        // Create URI for the domain
                        var cleanDomain = domain.TrimStart('.');
                        var uri = new Uri($"https://{cleanDomain}");

                        // Get cookies for this specific domain
                        var domainCookies = container.GetCookies(uri);
                        foreach (Cookie cookie in domainCookies)
                        {
                            cookies.Add(cookie);
                        }

                        Debug.WriteLine($"Found {domainCookies.Count} cookies for domain: {domain}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error getting cookies for domain {domain}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error parsing cache key {cacheKey}: {ex.Message}");
            }

            return cookies;
        }

        /// <summary>
        /// Converts persistent storage format back to CookieContainer
        /// </summary>
        private static CookieContainer ConvertFromPersistentContainer(PersistentCookieContainer persistentContainer)
        {
            var container = new CookieContainer { PerDomainCapacity = 100 };

            foreach (var cookieData in persistentContainer.Cookies)
            {
                // Skip expired cookies
                if (cookieData.Expires.HasValue && cookieData.Expires.Value <= DateTime.UtcNow)
                    continue;

                try
                {
                    var cookie = new Cookie(cookieData.Name, cookieData.Value, cookieData.Path, cookieData.Domain)
                    {
                        HttpOnly = cookieData.HttpOnly,
                        Secure = cookieData.Secure
                    };

                    if (cookieData.Expires.HasValue)
                        cookie.Expires = cookieData.Expires.Value;

                    container.Add(cookie);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error recreating cookie {cookieData.Name}: {ex.Message}");
                }
            }

            return container;
        }

        /// <summary>
        /// Validates persistent cookie data against current profile and timestamp
        /// </summary>
        private static bool IsValidCookieData(PersistentCookieData data, CookieProfile currentProfile)
        {
            // Check profile match
            if (currentProfile == null || string.IsNullOrEmpty(currentProfile.Profile))
            {
                Debug.WriteLine("Cookie validation failed: No current profile");
                return false;
            }

            if (data.ProfilePath != currentProfile.Profile)
            {
                Debug.WriteLine($"Cookie validation failed: Profile mismatch. Stored: {data.ProfilePath}, Current: {currentProfile.Profile}");
                return false;
            }

            // Check age
            if ((DateTime.UtcNow - data.SavedAt) > MaxCookieAge)
            {
                Debug.WriteLine($"Cookie validation failed: Data too old ({DateTime.UtcNow - data.SavedAt:g})");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Saves encrypted cookie data to disk
        /// </summary>
        private static async Task SaveEncryptedDataAsync(PersistentCookieData data)
        {
            var jsonString = JsonConvert.SerializeObject(data, Formatting.None);
            var encryptedData = Serializator.SerializeConcurrentDictionary(
                new ConcurrentDictionary<string, string> { ["cookies"] = jsonString });

            await Task.Run(() => File.WriteAllText(CookieFilePath, encryptedData));
        }

        /// <summary>
        /// Loads and decrypts cookie data from disk
        /// </summary>
        private static async Task<PersistentCookieData> LoadEncryptedDataAsync()
        {
            var encryptedData = await Task.Run(() => File.ReadAllText(CookieFilePath));
            var decryptedDict = Serializator.DeserializeConcurrentDictionary<string, string>(encryptedData);

            if (decryptedDict.TryGetValue("cookies", out var jsonString))
            {
                return JsonConvert.DeserializeObject<PersistentCookieData>(jsonString);
            }

            return null;
        }

        /// <summary>
        /// Gets all cookies from a CookieContainer using multiple approaches
        /// </summary>
        private static IEnumerable<Cookie> GetAllCookies(CookieContainer container)
        {
            var cookies = new List<Cookie>();

            try
            {
                // Method 1: Try reflection approach (works on some .NET versions)
                var reflectionCookies = GetCookiesViaReflection(container);
                if (reflectionCookies.Any())
                {
                    Debug.WriteLine($"Extracted {reflectionCookies.Count()} cookies via reflection");
                    return reflectionCookies;
                }

                // Method 2: Try GetCookies method with common domains (fallback)
                var commonDomains = new[] { ".ebay.com", ".ebay.co.uk", ".ebay.de", ".ebay.fr", ".ebay.it", ".ebay.es", ".amazon.com" };
                foreach (var domain in commonDomains)
                {
                    try
                    {
                        var uri = new Uri($"https://{domain.TrimStart('.')}");
                        var domainCookies = container.GetCookies(uri);
                        foreach (Cookie cookie in domainCookies)
                        {
                            cookies.Add(cookie);
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error getting cookies for domain {domain}: {ex.Message}");
                    }
                }

                Debug.WriteLine($"Extracted {cookies.Count} cookies via domain enumeration");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error extracting cookies from container: {ex.Message}");
            }

            return cookies;
        }

        /// <summary>
        /// Attempts to extract cookies using reflection (may not work on all .NET versions)
        /// </summary>
        private static IEnumerable<Cookie> GetCookiesViaReflection(CookieContainer container)
        {
            var cookies = new List<Cookie>();

            try
            {
                var table = container.GetType().InvokeMember("m_domainTable",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.GetField |
                    System.Reflection.BindingFlags.Instance, null, container, new object[] { });

                if (table != null)
                {
                    foreach (var tableEntry in (System.Collections.IDictionary)table)
                    {
                        var domain = tableEntry.GetType().GetProperty("Value")?.GetValue(tableEntry, null);
                        if (domain == null) continue;

                        var list = domain.GetType().InvokeMember("m_list",
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.GetField |
                            System.Reflection.BindingFlags.Instance, null, domain, new object[] { });

                        if (list is System.Collections.IEnumerable enumerable)
                        {
                            foreach (var cookie in enumerable)
                            {
                                if (cookie is Cookie c)
                                {
                                    cookies.Add(c);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Reflection-based cookie extraction failed: {ex.Message}");
            }

            return cookies;
        }
    }

    /// <summary>
    /// Serializable representation of individual cookie data
    /// </summary>
    public class CookieData
    {
        public string Name { get; set; }
        public string Value { get; set; }
        public string Domain { get; set; }
        public string Path { get; set; }
        public DateTime? Expires { get; set; }
        public bool HttpOnly { get; set; }
        public bool Secure { get; set; }
    }

    /// <summary>
    /// Serializable container for domain-specific cookies
    /// </summary>
    public class PersistentCookieContainer
    {
        public List<CookieData> Cookies { get; set; } = new List<CookieData>();
        public DateTime SavedAt { get; set; }
        public string CacheKey { get; set; }
    }

    /// <summary>
    /// Top-level container for persistent cookie data with validation metadata
    /// </summary>
    public class PersistentCookieData
    {
        public string ProfilePath { get; set; }
        public DateTime SavedAt { get; set; }
        public Dictionary<string, PersistentCookieContainer> DomainContainers { get; set; } = new Dictionary<string, PersistentCookieContainer>();
    }
}
