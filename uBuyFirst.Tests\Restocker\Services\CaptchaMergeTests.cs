using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class CaptchaMergeTests
    {
        [TestInitialize]
        public void Setup()
        {
            // Clear any existing cooldown state before each test
            CaptchaCooldownManager.ClearCooldown();
        }

        [TestMethod]
        public async Task HandleCaptchaDetectionAsync_WithCooldownTrue_ShouldStartCooldown()
        {
            // Arrange
            var dataList = new DataList { ItemID = "123456789", Title = "Test Item" };
            var errorMessage = "Session ID not found in session page HTML.";

            // Act
            await CaptchaCooldownManager.HandleCaptchaDetectionAsync(dataList, errorMessage, startCooldown: true);

            // Assert
            Assert.IsTrue(CaptchaCooldownManager.IsInCooldown, "Cooldown should be active when startCooldown is true");
            Assert.AreEqual(CooldownType.Captcha, CaptchaCooldownManager.GetCooldownType(), "Should be captcha cooldown type");
        }

        [TestMethod]
        public async Task HandleCaptchaDetectionAsync_WithCooldownFalse_ShouldNotStartCooldown()
        {
            // Arrange
            var dataList = new DataList { ItemID = "123456789", Title = "Test Item" };
            var errorMessage = "Session ID not found in session page HTML.";

            // Act
            await CaptchaCooldownManager.HandleCaptchaDetectionAsync(dataList, errorMessage, startCooldown: false);

            // Assert
            Assert.IsFalse(CaptchaCooldownManager.IsInCooldown, "Cooldown should not be active when startCooldown is false");
            Assert.AreEqual(CooldownType.None, CaptchaCooldownManager.GetCooldownType(), "Should not have any cooldown type");
        }

        [TestMethod]
        public async Task HandleCaptchaDetectionAsync_DefaultParameter_ShouldStartCooldown()
        {
            // Arrange
            var dataList = new DataList { ItemID = "123456789", Title = "Test Item" };
            var errorMessage = "Session ID not found in session page HTML.";

            // Act - using default parameter (should be true)
            await CaptchaCooldownManager.HandleCaptchaDetectionAsync(dataList, errorMessage);

            // Assert
            Assert.IsTrue(CaptchaCooldownManager.IsInCooldown, "Cooldown should be active with default parameter");
            Assert.AreEqual(CooldownType.Captcha, CaptchaCooldownManager.GetCooldownType(), "Should be captcha cooldown type");
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Clear cooldown state after each test
            CaptchaCooldownManager.ClearCooldown();
        }
    }
}
