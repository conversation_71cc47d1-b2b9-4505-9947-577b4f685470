using System;
using System.Threading;

namespace uBuyFirst.Search
{
    /// <summary>
    /// Tracks progress during the initial search phase
    /// These counters are never reset during initial search to provide reliable completion detection
    /// </summary>
    public class InitialSearchProgress
    {
        private int _itemsFound;
        private int _itemsProcessed;
        private int _requestsCompleted;
        private int _keywordsProcessed;
        private readonly object _lock = new object();

        /// <summary>
        /// Number of items found during initial search (never reset)
        /// </summary>
        public int ItemsFound => _itemsFound;

        /// <summary>
        /// Number of items fully processed during initial search (never reset)
        /// </summary>
        public int ItemsProcessed => _itemsProcessed;

        /// <summary>
        /// Number of API requests completed during initial search
        /// </summary>
        public int RequestsCompleted => _requestsCompleted;

        /// <summary>
        /// Number of keywords that have completed their initial search
        /// </summary>
        public int KeywordsProcessed => _keywordsProcessed;

        /// <summary>
        /// When the initial search started
        /// </summary>
        public DateTime StartTime { get; private set; }

        /// <summary>
        /// When the last item was found (for smart completion detection)
        /// </summary>
        public DateTime LastItemFoundTime { get; private set; }

        /// <summary>
        /// When all search requests were completed (for smart completion detection)
        /// </summary>
        public DateTime? AllRequestsCompleteTime { get; private set; }

        /// <summary>
        /// How long the initial search has been running
        /// </summary>
        public TimeSpan ElapsedTime => DateTime.UtcNow - StartTime;

        /// <summary>
        /// How long since the last item was found
        /// </summary>
        public TimeSpan TimeSinceLastItem => DateTime.UtcNow - LastItemFoundTime;

        /// <summary>
        /// How long since all requests were completed (null if not complete yet)
        /// </summary>
        public TimeSpan? TimeSinceAllRequestsComplete =>
            AllRequestsCompleteTime.HasValue ? DateTime.UtcNow - AllRequestsCompleteTime.Value : null;

        /// <summary>
        /// Whether dual-phase search is enabled
        /// </summary>
        public bool IsDualPhaseEnabled { get; set; }

        /// <summary>
        /// Whether the price-first phase is complete
        /// </summary>
        public bool IsPriceFirstPhaseComplete { get; set; }

        /// <summary>
        /// Whether the regular phase is complete
        /// </summary>
        public bool IsRegularPhaseComplete { get; set; }

        /// <summary>
        /// Initializes progress tracking
        /// </summary>
        public void Initialize(bool isDualPhaseEnabled)
        {
            lock (_lock)
            {
                StartTime = DateTime.UtcNow;
                LastItemFoundTime = DateTime.UtcNow; // Start the timer
                AllRequestsCompleteTime = null;
                IsDualPhaseEnabled = isDualPhaseEnabled;
                IsPriceFirstPhaseComplete = false;
                IsRegularPhaseComplete = false;
                _itemsFound = 0;
                _itemsProcessed = 0;
                _requestsCompleted = 0;
                _keywordsProcessed = 0;
            }
        }

        /// <summary>
        /// Increments the items found counter
        /// </summary>
        public void IncrementItemsFound(int count = 1)
        {
            Interlocked.Add(ref _itemsFound, count);

            // Update last item found time for smart completion detection
            lock (_lock)
            {
                LastItemFoundTime = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Increments the items processed counter
        /// </summary>
        public void IncrementItemsProcessed(int count = 1)
        {
            Interlocked.Add(ref _itemsProcessed, count);
        }

        /// <summary>
        /// Increments the requests completed counter
        /// </summary>
        public void IncrementRequestsCompleted(int count = 1)
        {
            Interlocked.Add(ref _requestsCompleted, count);
        }

        /// <summary>
        /// Increments the keywords processed counter
        /// </summary>
        public void IncrementKeywordsProcessed(int count = 1)
        {
            Interlocked.Add(ref _keywordsProcessed, count);
        }

        /// <summary>
        /// Marks the price-first phase as complete
        /// </summary>
        public void MarkPriceFirstPhaseComplete()
        {
            lock (_lock)
            {
                IsPriceFirstPhaseComplete = true;
            }
        }

        /// <summary>
        /// Marks the regular phase as complete
        /// </summary>
        public void MarkRegularPhaseComplete()
        {
            lock (_lock)
            {
                IsRegularPhaseComplete = true;
            }
        }

        /// <summary>
        /// Marks that all search requests have been completed
        /// </summary>
        public void MarkAllRequestsComplete()
        {
            lock (_lock)
            {
                if (!AllRequestsCompleteTime.HasValue)
                {
                    AllRequestsCompleteTime = DateTime.UtcNow;
                }
            }
        }

        /// <summary>
        /// Gets whether all phases are complete
        /// </summary>
        public bool AreAllPhasesComplete
        {
            get
            {
                lock (_lock)
                {
                    return IsDualPhaseEnabled
                        ? (IsPriceFirstPhaseComplete && IsRegularPhaseComplete)
                        : IsRegularPhaseComplete;
                }
            }
        }

        /// <summary>
        /// Gets a summary of current progress for debugging
        /// </summary>
        public string GetProgressSummary()
        {
            lock (_lock)
            {
                var timeSinceAllComplete = TimeSinceAllRequestsComplete?.ToString(@"mm\:ss") ?? "N/A";
                return $"Items: {ItemsFound} found, {ItemsProcessed} processed | " +
                       $"Requests: {RequestsCompleted} | Keywords: {KeywordsProcessed} | " +
                       $"Elapsed: {ElapsedTime:mm\\:ss} | Since last item: {TimeSinceLastItem:mm\\:ss} | " +
                       $"Since all complete: {timeSinceAllComplete} | " +
                       $"Phases: PriceFirst={IsPriceFirstPhaseComplete}, Regular={IsRegularPhaseComplete}";
            }
        }
    }
}
