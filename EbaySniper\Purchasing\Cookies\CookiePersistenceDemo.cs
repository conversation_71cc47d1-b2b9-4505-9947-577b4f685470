using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using uBuyFirst.Other;

namespace uBuyFirst.Purchasing.Cookies
{
    /// <summary>
    /// Demonstration class to show cookie persistence functionality
    /// This can be used for manual testing and verification
    /// </summary>
    public static class CookiePersistenceDemo
    {
        /// <summary>
        /// Demonstrates the complete cookie persistence workflow
        /// </summary>
        public static async Task RunDemoAsync()
        {
            Debug.WriteLine("=== Cookie Persistence Demo ===");

            try
            {
                // 1. Setup test profile
                var testProfile = new CookieProfile
                {
                    Name = "Demo Profile",
                    Profile = "demo-profile-path"
                };

                Debug.WriteLine($"1. Created test profile: {testProfile.Name}");

                // 2. Create sample cookies
                var cookieContainer = CreateSampleCookies();
                var domainCache = new ConcurrentDictionary<string, CookieContainer>();
                domainCache.TryAdd(".ebay.com", cookieContainer);

                Debug.WriteLine($"2. Created sample cookies: {cookieContainer.Count} cookies");

                // 3. Save cookies to disk
                await CookiePersistenceService.SaveCookiesAsync(domainCache, testProfile);
                Debug.WriteLine("3. Saved cookies to persistent storage");

                // 4. Verify file was created
                var cookieFilePath = Path.Combine(Folders.Settings, "cookies.dat");
                if (File.Exists(cookieFilePath))
                {
                    var fileInfo = new FileInfo(cookieFilePath);
                    Debug.WriteLine($"4. Cookie file created: {fileInfo.Length} bytes");
                }
                else
                {
                    Debug.WriteLine("4. ERROR: Cookie file was not created!");
                    return;
                }

                // 5. Load cookies from disk
                var loadedCookies = await CookiePersistenceService.LoadCookiesAsync(new[] { ".ebay.com" }, testProfile);
                if (loadedCookies != null && loadedCookies.ContainsKey(".ebay.com"))
                {
                    var loadedContainer = loadedCookies[".ebay.com"];
                    Debug.WriteLine($"5. Loaded cookies from disk: {loadedContainer.Count} cookies");
                }
                else
                {
                    Debug.WriteLine("5. ERROR: Failed to load cookies from disk!");
                    return;
                }

                // 6. Test profile validation (should fail with different profile)
                var differentProfile = new CookieProfile
                {
                    Name = "Different Profile",
                    Profile = "different-profile-path"
                };

                var invalidLoad = await CookiePersistenceService.LoadCookiesAsync(new[] { ".ebay.com" }, differentProfile);
                if (invalidLoad == null)
                {
                    Debug.WriteLine("6. Profile validation working: Different profile rejected");
                }
                else
                {
                    Debug.WriteLine("6. ERROR: Profile validation failed!");
                }

                // 7. Test CookieManager integration
                CookieManager.Profile = testProfile;
                
                // Clear cache to force loading from persistent storage
                CookieManager.ClearCookieCache();
                
                // This should recreate the file since ClearCookieCache deletes it
                await CookiePersistenceService.SaveCookiesAsync(domainCache, testProfile);
                
                var managerCookies = CookieManager.GetCookies(new[] { ".ebay.com" });
                Debug.WriteLine($"7. CookieManager integration: Retrieved {managerCookies.Count} cookies");

                // 8. Test logout detection (should delete persistent cookies)
                CookieManager.ClearCookieCache();
                if (!File.Exists(cookieFilePath))
                {
                    Debug.WriteLine("8. Logout detection working: Persistent cookies deleted");
                }
                else
                {
                    Debug.WriteLine("8. ERROR: Logout detection failed!");
                }

                Debug.WriteLine("=== Demo completed successfully! ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in demo: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Creates sample cookies for testing
        /// </summary>
        private static CookieContainer CreateSampleCookies()
        {
            var container = new CookieContainer { PerDomainCapacity = 100 };

            // Add some sample eBay cookies
            var cookies = new[]
            {
                new Cookie("ebay_session", "sample_session_value", "/", ".ebay.com")
                {
                    HttpOnly = true,
                    Secure = true,
                    Expires = DateTime.UtcNow.AddDays(1)
                },
                new Cookie("user_pref", "sample_preference", "/", ".ebay.com")
                {
                    HttpOnly = false,
                    Secure = false,
                    Expires = DateTime.UtcNow.AddDays(7)
                },
                new Cookie("auth_token", "sample_auth_token", "/", ".ebay.com")
                {
                    HttpOnly = true,
                    Secure = true,
                    Expires = DateTime.UtcNow.AddHours(2)
                }
            };

            foreach (var cookie in cookies)
            {
                container.Add(cookie);
            }

            return container;
        }

        /// <summary>
        /// Demonstrates encryption and decryption of cookie data
        /// </summary>
        public static void DemonstrateEncryption()
        {
            Debug.WriteLine("=== Encryption Demo ===");

            try
            {
                // Create test data
                var testData = new PersistentCookieData
                {
                    ProfilePath = "test-profile",
                    SavedAt = DateTime.UtcNow,
                    DomainContainers = new System.Collections.Generic.Dictionary<string, PersistentCookieContainer>()
                };

                // Serialize to JSON
                var jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(testData);
                Debug.WriteLine($"Original JSON length: {jsonString.Length}");

                // Encrypt using existing pattern
                var encryptedData = Serializator.SerializeConcurrentDictionary(
                    new ConcurrentDictionary<string, string> { ["cookies"] = jsonString });
                Debug.WriteLine($"Encrypted data length: {encryptedData.Length}");

                // Decrypt
                var decryptedDict = Serializator.DeserializeConcurrentDictionary<string, string>(encryptedData);
                if (decryptedDict.TryGetValue("cookies", out var decryptedJson))
                {
                    var decryptedData = Newtonsoft.Json.JsonConvert.DeserializeObject<PersistentCookieData>(decryptedJson);
                    Debug.WriteLine($"Decrypted profile: {decryptedData.ProfilePath}");
                    Debug.WriteLine("Encryption/decryption working correctly!");
                }
                else
                {
                    Debug.WriteLine("ERROR: Decryption failed!");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in encryption demo: {ex.Message}");
            }

            Debug.WriteLine("=== Encryption Demo Complete ===");
        }

        /// <summary>
        /// Runs a quick validation test
        /// </summary>
        public static async Task RunQuickValidationAsync()
        {
            Debug.WriteLine("=== Quick Validation Test ===");

            var profile = new CookieProfile { Name = "Test", Profile = "test-path" };
            var container = CreateSampleCookies();
            var cache = new ConcurrentDictionary<string, CookieContainer>();
            cache.TryAdd(".ebay.com", container);

            // Save and load
            await CookiePersistenceService.SaveCookiesAsync(cache, profile);
            var loaded = await CookiePersistenceService.LoadCookiesAsync(new[] { ".ebay.com" }, profile);

            if (loaded != null && loaded.ContainsKey(".ebay.com"))
            {
                Debug.WriteLine("✓ Save and load working");
            }
            else
            {
                Debug.WriteLine("✗ Save and load failed");
            }

            // Test cleanup
            CookiePersistenceService.DeleteStoredCookies();
            var afterDelete = await CookiePersistenceService.LoadCookiesAsync(new[] { ".ebay.com" }, profile);

            if (afterDelete == null)
            {
                Debug.WriteLine("✓ Delete working");
            }
            else
            {
                Debug.WriteLine("✗ Delete failed");
            }

            Debug.WriteLine("=== Quick Validation Complete ===");
        }
    }
}
